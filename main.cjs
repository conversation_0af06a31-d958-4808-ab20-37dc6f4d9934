const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const net = require('net');
const os = require('os');

let mainWindow;

// Find available port for development
async function findAvailablePort(startPort = 3000) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(startPort, () => {
      const port = server.address().port;
      server.close(() => resolve(port));
    });
    server.on('error', () => {
      resolve(findAvailablePort(startPort + 1));
    });
  });
}

// Create main window
async function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false // Allow local file access
    },
    icon: path.join(__dirname, 'assets/logo2png.png'), // Add app icon
    title: 'نظام المحاسبي - iCalDZ', // Default Arabic title
    show: false // Don't show until ready
  });

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Load your app
  if (process.env.NODE_ENV === 'development') {
    const port = await findAvailablePort(3000);
    const devUrl = `http://localhost:${port}`;
    console.log(`🚀 Loading development server from: ${devUrl}`);
    mainWindow.loadURL(devUrl);
    mainWindow.webContents.openDevTools();
  } else {
    const indexPath = path.join(__dirname, 'dist/index.html');
    console.log(`📁 Loading production build from: ${indexPath}`);
    mainWindow.loadFile(indexPath);
  }

  // Handle navigation errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error(`Failed to load: ${validatedURL} - ${errorDescription}`);
    // Try to load local file as fallback
    if (validatedURL.includes('localhost')) {
      mainWindow.loadFile(path.join(__dirname, 'dist/index.html'));
    }
  });
}

ipcMain.handle('get-machine-info', async () => {
  try {
    return {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      cpus: os.cpus().length,
      totalmem: os.totalmem(),
      userInfo: os.userInfo()
    };
  } catch (error) {
    console.error('Error getting machine info:', error);
    return { error: 'Failed to get machine info' };
  }
});

// IPC Handlers for language management
ipcMain.handle('get-system-language', async () => {
  try {
    const locale = app.getLocale();
    console.log('System locale:', locale);

    // Map system locale to supported languages
    if (locale.startsWith('ar')) return 'ar';
    if (locale.startsWith('fr')) return 'fr';
    if (locale.startsWith('en')) return 'en';

    // Default to Arabic
    return 'ar';
  } catch (error) {
    console.error('Error getting system language:', error);
    return 'ar';
  }
});

ipcMain.handle('set-app-language', async (event, langCode) => {
  try {
    console.log('Setting app language to:', langCode);

    // Update window title based on language
    const titles = {
      ar: 'نظام المحاسبي - iCalDZ',
      fr: 'Système Comptable - iCalDZ',
      en: 'Accounting System - iCalDZ'
    };

    if (mainWindow && titles[langCode]) {
      mainWindow.setTitle(titles[langCode]);
    }

    return { success: true, language: langCode };
  } catch (error) {
    console.error('Error setting app language:', error);
    return { error: 'Failed to set language' };
  }
});

// App ready
app.whenReady().then(async () => {
  console.log('🚀 iCalDZ Accounting System Starting...');
  await createMainWindow();
});

// Quit when all windows closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', async () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    await createMainWindow();
  }
});

// Handle certificate errors in development
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.includes('localhost')) {
    // Ignore certificate errors for localhost
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
});
