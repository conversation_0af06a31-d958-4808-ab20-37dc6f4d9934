/**
 * Activation Dialog Component
 * 
 * This component provides the user interface for activating the iCalDZ application.
 * It includes form validation, error handling, and bilingual support.
 */

import React, { useState, useEffect, useContext } from 'react';
import { LanguageContext } from '../LanguageContext';
import { activationService } from '../services/ActivationService';
import './ActivationDialog.css';

const ActivationDialog = ({ onActivationSuccess, onClose }) => {
  const { language, getTranslation } = useContext(LanguageContext);
  const [activationCode, setActivationCode] = useState('');
  const [userName, setUserName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [deviceId, setDeviceId] = useState('');

  useEffect(() => {
    // Get device ID for display
    const initializeDialog = async () => {
      await activationService.initialize();
      setDeviceId(activationService.deviceId);
    };
    initializeDialog();
  }, []);

  const t = (key, fallback) => getTranslation(key, language) || fallback;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Validate input
      if (!activationCode.trim()) {
        throw new Error(t('activationCodeRequired', 'Activation code is required'));
      }

      // Attempt activation
      const result = await activationService.activate(activationCode.trim(), userName.trim());

      if (result.success) {
        // Show success message
        setError('');
        
        // Call success callback
        if (onActivationSuccess) {
          onActivationSuccess(result.data);
        }
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError(error.message || t('activationFailed', 'Activation failed'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatActivationCode = (value) => {
    // Remove any non-alphanumeric characters except hyphens
    const cleaned = value.replace(/[^A-Z0-9-]/gi, '').toUpperCase();
    
    // Add hyphens in the correct positions for ICAL-2025-XXXX-XXXX-XXXX format
    if (cleaned.length <= 4) return cleaned;
    if (cleaned.length <= 8) return `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
    if (cleaned.length <= 13) return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8)}`;
    if (cleaned.length <= 18) return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8, 12)}-${cleaned.slice(12)}`;
    if (cleaned.length <= 23) return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8, 12)}-${cleaned.slice(12, 16)}-${cleaned.slice(16)}`;
    
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8, 12)}-${cleaned.slice(12, 16)}-${cleaned.slice(16, 20)}`;
  };

  const handleCodeChange = (e) => {
    const formatted = formatActivationCode(e.target.value);
    setActivationCode(formatted);
  };

  return (
    <div className="activation-dialog-overlay">
      <div className="activation-dialog">
        <div className="activation-header">
          <div className="activation-logo">
            <img src="/logosvg.svg" alt="iCalDZ" className="logo-image" />
          </div>
          <h2 className="activation-title">
            {t('activateApplication', 'تفعيل التطبيق')}
          </h2>
          <p className="activation-subtitle">
            {t('activationRequired', 'يتطلب تفعيل التطبيق للمتابعة')}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="activation-form">
          <div className="form-group">
            <label htmlFor="activationCode" className="form-label">
              {t('activationCode', 'كود التفعيل')}
            </label>
            <input
              type="text"
              id="activationCode"
              value={activationCode}
              onChange={handleCodeChange}
              placeholder={t('enterActivationCode', 'أدخل كود التفعيل (ICAL-2025-XXXX-XXXX-XXXX)')}
              className="form-input activation-code-input"
              maxLength={24}
              required
              disabled={isLoading}
              dir="ltr"
            />
            <div className="input-hint">
              {t('codeFormat', 'تنسيق الكود: ICAL-2025-XXXX-XXXX-XXXX')}
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="userName" className="form-label">
              {t('userName', 'اسم المستخدم')} ({t('optional', 'اختياري')})
            </label>
            <input
              type="text"
              id="userName"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder={t('enterUserName', 'أدخل اسم المستخدم')}
              className="form-input"
              disabled={isLoading}
            />
          </div>

          <div className="device-info">
            <div className="device-id-section">
              <label className="form-label">{t('deviceId', 'معرف الجهاز')}</label>
              <div className="device-id-display">
                {deviceId ? deviceId.substring(0, 16) + '...' : t('loading', 'جاري التحميل...')}
              </div>
              <div className="device-info-note">
                {t('deviceIdNote', 'هذا الكود مرتبط بهذا الجهاز فقط')}
              </div>
            </div>
          </div>

          {error && (
            <div className="error-message">
              <div className="error-icon">⚠️</div>
              <div className="error-text">{error}</div>
            </div>
          )}

          <div className="form-actions">
            <button
              type="submit"
              className="btn-activate"
              disabled={isLoading || !activationCode.trim()}
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner"></div>
                  {t('activating', 'جاري التفعيل...')}
                </>
              ) : (
                t('activate', 'تفعيل')
              )}
            </button>
          </div>
        </form>

        <div className="activation-footer">
          <div className="support-info">
            <p>{t('needHelp', 'تحتاج مساعدة؟')}</p>
            <p>{t('contactSupport', 'تواصل مع الدعم الفني: <EMAIL>')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivationDialog;
