/* License Status Component Styles */

.license-status {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 1rem 0;
}

.license-status.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  color: #6c757d;
}

.license-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid #dee2e6;
}

.license-icon {
  font-size: 3rem;
  flex-shrink: 0;
}

.license-info {
  flex: 1;
}

.license-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #212529;
}

.license-subtitle {
  font-size: 1rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.license-details {
  border-bottom: 1px solid #dee2e6;
}

.details-toggle {
  width: 100%;
  background: none;
  border: none;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  transition: all 0.3s ease;
}

.details-toggle:hover {
  background: #f8f9fa;
  color: #212529;
}

.arrow {
  transition: transform 0.3s ease;
  font-size: 0.8rem;
  color: #6c757d;
}

.arrow.up {
  transform: rotate(180deg);
}

.details-content {
  padding: 0 2rem 1.5rem 2rem;
  background: #f8f9fa;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #495057;
  flex-shrink: 0;
  margin-right: 1rem;
}

.detail-value {
  color: #212529;
  text-align: right;
  word-break: break-all;
}

.detail-value.code {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  letter-spacing: 1px;
  background: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.detail-value.device-id {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #6c757d;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.activated {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.license-actions {
  padding: 1.5rem 2rem;
  text-align: center;
}

.btn-primary,
.btn-secondary {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 160px;
}

.btn-primary {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #3a7270, #2d5856);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(73, 140, 138, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #498C8A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* RTL Support */
[dir="rtl"] .license-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .details-toggle {
  flex-direction: row-reverse;
}

[dir="rtl"] .detail-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .detail-label {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .detail-value {
  text-align: left;
}

/* Responsive Design */
@media (max-width: 768px) {
  .license-header {
    padding: 1.5rem;
    gap: 1rem;
  }
  
  .license-icon {
    font-size: 2.5rem;
  }
  
  .license-title {
    font-size: 1.3rem;
  }
  
  .details-toggle {
    padding: 1rem 1.5rem;
  }
  
  .details-content {
    padding: 0 1.5rem 1rem 1.5rem;
  }
  
  .license-actions {
    padding: 1rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .license-header {
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .detail-value {
    text-align: left;
    width: 100%;
  }
  
  .btn-primary,
  .btn-secondary {
    width: 100%;
    min-width: auto;
  }
  
  [dir="rtl"] .detail-row {
    align-items: flex-end;
  }
  
  [dir="rtl"] .detail-value {
    text-align: right;
  }
}
