/* Activation Dialog Styles */

.activation-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.activation-dialog {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.activation-header {
  text-align: center;
  padding: 2rem 2rem 1rem 2rem;
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
  border-radius: 20px 20px 0 0;
}

.activation-logo {
  margin-bottom: 1rem;
}

.logo-image {
  width: 80px;
  height: 80px;
  filter: brightness(0) invert(1);
}

.activation-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.activation-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.activation-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  background: #f8f9fa;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #498C8A;
  background: white;
  box-shadow: 0 0 0 3px rgba(73, 140, 138, 0.1);
}

.activation-code-input {
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  text-align: center;
  font-size: 1.1rem;
  text-transform: uppercase;
}

.input-hint {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.5rem;
  text-align: center;
  font-style: italic;
}

.device-info {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
}

.device-id-section .form-label {
  margin-bottom: 0.75rem;
  color: #495057;
}

.device-id-display {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #495057;
  text-align: center;
  letter-spacing: 1px;
}

.device-info-note {
  font-size: 0.8rem;
  color: #6c757d;
  text-align: center;
  margin-top: 0.5rem;
  font-style: italic;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: #721c24;
}

.error-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.error-text {
  font-weight: 500;
  line-height: 1.4;
}

.form-actions {
  text-align: center;
  margin-top: 2rem;
}

.btn-activate {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 180px;
  justify-content: center;
}

.btn-activate:hover:not(:disabled) {
  background: linear-gradient(135deg, #3a7270, #2d5856);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(73, 140, 138, 0.3);
}

.btn-activate:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.activation-footer {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-radius: 0 0 20px 20px;
  border-top: 1px solid #e9ecef;
}

.support-info {
  text-align: center;
  color: #6c757d;
}

.support-info p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.support-info p:first-child {
  font-weight: 600;
  color: #495057;
}

/* RTL Support */
[dir="rtl"] .activation-dialog {
  text-align: right;
}

[dir="rtl"] .error-message {
  flex-direction: row-reverse;
}

[dir="rtl"] .btn-activate {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 768px) {
  .activation-dialog {
    width: 95%;
    margin: 1rem;
  }
  
  .activation-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }
  
  .activation-form {
    padding: 1.5rem;
  }
  
  .activation-title {
    font-size: 1.5rem;
  }
  
  .logo-image {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .activation-dialog {
    width: 98%;
    margin: 0.5rem;
  }
  
  .activation-header {
    padding: 1rem;
  }
  
  .activation-form {
    padding: 1rem;
  }
  
  .activation-title {
    font-size: 1.3rem;
  }
  
  .form-input {
    padding: 0.875rem;
    font-size: 0.95rem;
  }
  
  .btn-activate {
    padding: 0.875rem 2rem;
    font-size: 1rem;
    min-width: 160px;
  }
}
