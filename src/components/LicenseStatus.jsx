/**
 * License Status Component
 * 
 * This component displays the current license status and provides
 * options to manage the activation.
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../LanguageContext.jsx';
import { activationService } from '../services/ActivationService';
import './LicenseStatus.css';

const LicenseStatus = ({ onReactivate }) => {
  const { currentLanguage, t } = useLanguage();
  const [activationStatus, setActivationStatus] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadActivationStatus();
  }, []);

  const loadActivationStatus = async () => {
    await activationService.initialize();
    const status = activationService.getActivationStatus();
    setActivationStatus(status);
  };

  // t function is already available from useLanguage hook

  const handleClearActivation = () => {
    if (window.confirm(t('confirmClearActivation', 'هل أنت متأكد من إزالة التفعيل؟ ستحتاج إلى كود تفعيل جديد.'))) {
      activationService.clearLocalActivation();
      if (onReactivate) {
        onReactivate();
      }
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return t('unknown', 'غير معروف');
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(currentLanguage === 'ar' ? 'ar-DZ' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return t('invalidDate', 'تاريخ غير صحيح');
    }
  };

  const maskActivationCode = (code) => {
    if (!code) return '';
    const parts = code.split('-');
    if (parts.length >= 3) {
      return `${parts[0]}-${parts[1]}-****-****-****`;
    }
    return code.substring(0, 8) + '****';
  };

  if (!activationStatus) {
    return (
      <div className="license-status loading">
        <div className="loading-spinner"></div>
        <span>{t('loadingStatus', 'جاري تحميل حالة الترخيص...')}</span>
      </div>
    );
  }

  return (
    <div className="license-status">
      <div className="license-header">
        <div className="license-icon">
          {activationStatus.isActivated ? '✅' : '❌'}
        </div>
        <div className="license-info">
          <h3 className="license-title">
            {activationStatus.isActivated 
              ? t('licenseActive', 'الترخيص نشط') 
              : t('licenseInactive', 'الترخيص غير نشط')
            }
          </h3>
          <p className="license-subtitle">
            {activationStatus.isActivated 
              ? t('applicationActivated', 'التطبيق مفعل ويعمل بشكل طبيعي') 
              : t('activationRequired', 'يتطلب تفعيل للمتابعة')
            }
          </p>
        </div>
      </div>

      {activationStatus.isActivated && activationStatus.activationData && (
        <div className="license-details">
          <button 
            className="details-toggle"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? t('hideDetails', 'إخفاء التفاصيل') : t('showDetails', 'عرض التفاصيل')}
            <span className={`arrow ${showDetails ? 'up' : 'down'}`}>▼</span>
          </button>

          {showDetails && (
            <div className="details-content">
              <div className="detail-row">
                <span className="detail-label">{t('activationCode', 'كود التفعيل')}:</span>
                <span className="detail-value code">
                  {maskActivationCode(activationStatus.activationData.activation_code)}
                </span>
              </div>

              <div className="detail-row">
                <span className="detail-label">{t('deviceId', 'معرف الجهاز')}:</span>
                <span className="detail-value device-id">
                  {activationStatus.deviceId ? 
                    activationStatus.deviceId.substring(0, 16) + '...' : 
                    t('unknown', 'غير معروف')
                  }
                </span>
              </div>

              <div className="detail-row">
                <span className="detail-label">{t('activatedAt', 'تاريخ التفعيل')}:</span>
                <span className="detail-value">
                  {formatDate(activationStatus.activationData.activated_at)}
                </span>
              </div>

              {activationStatus.activationData.user_name && (
                <div className="detail-row">
                  <span className="detail-label">{t('userName', 'اسم المستخدم')}:</span>
                  <span className="detail-value">
                    {activationStatus.activationData.user_name}
                  </span>
                </div>
              )}

              <div className="detail-row">
                <span className="detail-label">{t('status', 'الحالة')}:</span>
                <span className="detail-value status-badge activated">
                  {t('activated', 'مفعل')}
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="license-actions">
        {activationStatus.isActivated ? (
          <button 
            className="btn-secondary"
            onClick={handleClearActivation}
          >
            {t('clearActivation', 'إزالة التفعيل')}
          </button>
        ) : (
          <button 
            className="btn-primary"
            onClick={onReactivate}
          >
            {t('activateNow', 'تفعيل الآن')}
          </button>
        )}
      </div>
    </div>
  );
};

export default LicenseStatus;
