/* Activation Wrapper Styles */

.activation-wrapper {
  position: relative;
  min-height: 100vh;
}

/* License Status Button */
.license-status-button {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(73, 140, 138, 0.3);
  font-size: 0.9rem;
  font-weight: 600;
}

.license-status-button:hover {
  background: linear-gradient(135deg, #3a7270, #2d5856);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(73, 140, 138, 0.4);
}

.license-icon {
  font-size: 1.1rem;
}

.license-text {
  font-weight: 600;
}

/* Loading State */
.activation-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.loading-container {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.loading-logo {
  margin-bottom: 2rem;
}

.loading-logo .logo-image {
  width: 120px;
  height: 120px;
  opacity: 0.8;
}

/* Activation Logo in Form */
.activation-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.activation-logo .logo-image {
  width: 100px;
  height: 100px;
  object-fit: contain;
  filter: drop-shadow(0 4px 15px rgba(73, 140, 138, 0.3));
  transition: transform 0.3s ease;
}

.activation-logo .logo-image:hover {
  transform: scale(1.05);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(73, 140, 138, 0.2);
  border-top: 4px solid #498C8A;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #212529;
  margin: 0 0 1rem 0;
}

.loading-subtitle {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0;
  font-weight: 400;
}

/* Error State */
.activation-error {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.error-container {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.error-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #dc3545;
  margin: 0 0 1rem 0;
}

.error-message {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.btn-retry {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-retry:hover {
  background: linear-gradient(135deg, #3a7270, #2d5856);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(73, 140, 138, 0.3);
}

/* License Status Modal */
.license-status-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 0;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

/* Fallback State */
.activation-fallback {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.fallback-container {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.fallback-container h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #dc3545;
  margin: 0 0 1rem 0;
}

.fallback-container p {
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0 0 2rem 0;
}

.fallback-container button {
  background: linear-gradient(135deg, #498C8A, #3a7270);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fallback-container button:hover {
  background: linear-gradient(135deg, #3a7270, #2d5856);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(73, 140, 138, 0.3);
}

/* RTL Support */
[dir="rtl"] .license-status-button {
  right: auto;
  left: 20px;
}

[dir="rtl"] .modal-header {
  flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 768px) {
  .license-status-button {
    top: 10px;
    right: 10px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .modal-content {
    width: 95%;
    margin: 1rem;
  }
  
  .modal-header {
    padding: 1rem 1.5rem;
  }
  
  .modal-header h3 {
    font-size: 1.3rem;
  }
  
  .loading-container,
  .error-container,
  .fallback-container {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .loading-title,
  .error-title {
    font-size: 1.5rem;
  }
  
  .loading-logo .logo-image {
    width: 80px;
    height: 80px;
  }
  
  [dir="rtl"] .license-status-button {
    left: 10px;
  }
}

@media (max-width: 480px) {
  .license-status-button {
    position: relative;
    top: auto;
    right: auto;
    margin: 10px;
    width: calc(100% - 20px);
    justify-content: center;
  }
  
  .modal-content {
    width: 98%;
    margin: 0.5rem;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
  }
  
  [dir="rtl"] .license-status-button {
    left: auto;
    margin: 10px;
  }
}
