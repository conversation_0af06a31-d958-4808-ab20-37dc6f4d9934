/**
 * Web Security Service for iCalDZ
 * 
 * A simplified security service for web environments that don't have
 * access to Electron-specific APIs.
 */

import CryptoJS from 'crypto-js';

class WebSecurityService {
  constructor() {
    this.securityKey = 'iCalDZ-Security-2025-v1.0';
    this.sessionKey = 'icaldz-security-session';
    this.isInitialized = false;
    this.securityViolations = [];
    this.lastSecurityCheck = null;
    this.hardwareFingerprint = null;
  }

  /**
   * Initialize security service
   */
  async initialize() {
    try {
      console.log('🔒 Initializing web security service...');
      
      // Generate hardware fingerprint
      this.hardwareFingerprint = await this.generateHardwareFingerprint();
      
      this.isInitialized = true;
      console.log('✅ Web security service initialized');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize web security service:', error);
      return false;
    }
  }

  /**
   * Generate hardware fingerprint for web environment
   */
  async generateHardwareFingerprint() {
    try {
      const components = [];
      
      // Screen resolution
      if (typeof window !== 'undefined' && window.screen) {
        components.push(`${window.screen.width}x${window.screen.height}`);
        components.push(`${window.screen.colorDepth}`);
      }
      
      // Timezone
      try {
        components.push(Intl.DateTimeFormat().resolvedOptions().timeZone);
      } catch (error) {
        components.push(new Date().getTimezoneOffset().toString());
      }
      
      // Language
      if (typeof navigator !== 'undefined') {
        components.push(navigator.language || navigator.userLanguage || 'unknown');
      }
      
      // Platform
      if (typeof navigator !== 'undefined') {
        components.push(navigator.platform || 'unknown');
      }
      
      // CPU cores (if available)
      if (typeof navigator !== 'undefined' && navigator.hardwareConcurrency) {
        components.push(navigator.hardwareConcurrency.toString());
      }
      
      // User agent (partial)
      if (typeof navigator !== 'undefined') {
        components.push(CryptoJS.SHA256(navigator.userAgent).toString().substring(0, 16));
      }
      
      // Create fingerprint hash
      const fingerprintData = components.join('|');
      const fingerprint = CryptoJS.SHA256(fingerprintData).toString();
      
      console.log('🔍 Web hardware fingerprint generated');
      return fingerprint;
      
    } catch (error) {
      console.error('❌ Failed to generate web hardware fingerprint:', error);
      return CryptoJS.SHA256('fallback-fingerprint-' + Date.now()).toString();
    }
  }

  /**
   * Handle security violations (simplified for web)
   */
  handleSecurityViolation(violationType) {
    const violation = {
      type: violationType,
      timestamp: new Date().toISOString(),
      fingerprint: this.hardwareFingerprint
    };
    
    this.securityViolations.push(violation);
    
    console.warn('🚨 Security violation detected:', violationType);
    
    // Limit violations array size
    if (this.securityViolations.length > 50) {
      this.securityViolations = this.securityViolations.slice(-25);
    }
  }

  /**
   * Validate session integrity
   */
  validateSession() {
    try {
      const sessionData = localStorage.getItem(this.sessionKey);
      
      if (!sessionData) {
        return this.createNewSession();
      }
      
      const decrypted = CryptoJS.AES.decrypt(sessionData, this.securityKey);
      const session = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
      
      // Check session validity
      const now = Date.now();
      const sessionAge = now - new Date(session.created).getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (sessionAge > maxAge) {
        return this.createNewSession();
      }
      
      // Check fingerprint
      if (session.fingerprint !== this.hardwareFingerprint) {
        this.handleSecurityViolation('Session fingerprint mismatch');
        return this.createNewSession();
      }
      
      return true;
      
    } catch (error) {
      console.error('❌ Session validation failed:', error);
      return this.createNewSession();
    }
  }

  /**
   * Create new security session
   */
  createNewSession() {
    try {
      const session = {
        id: CryptoJS.lib.WordArray.random(16).toString(),
        created: new Date().toISOString(),
        fingerprint: this.hardwareFingerprint,
        violations: 0
      };
      
      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(session), 
        this.securityKey
      ).toString();
      
      localStorage.setItem(this.sessionKey, encrypted);
      
      console.log('🔐 New web security session created');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to create web security session:', error);
      return false;
    }
  }

  /**
   * Get security status
   */
  getSecurityStatus() {
    return {
      isInitialized: this.isInitialized,
      hardwareFingerprint: this.hardwareFingerprint,
      violationCount: this.securityViolations.length,
      lastCheck: this.lastSecurityCheck,
      recentViolations: this.securityViolations.slice(-5)
    };
  }

  /**
   * Clear security violations
   */
  clearViolations() {
    this.securityViolations = [];
    console.log('🧹 Web security violations cleared');
  }
}

// Create singleton instance
export const webSecurityService = new WebSecurityService();

// Export default
export default webSecurityService;
