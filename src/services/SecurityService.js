/**
 * Security Service for iCalDZ
 * 
 * This service provides enhanced security features including:
 * - Anti-tampering detection
 * - Hardware fingerprinting
 * - Session validation
 * - Security monitoring
 */

import CryptoJS from 'crypto-js';
import { machineIdSync } from 'node-machine-id';

class SecurityService {
  constructor() {
    this.securityKey = 'iCalDZ-Security-2025-v1.0';
    this.sessionKey = 'icaldz-security-session';
    this.isInitialized = false;
    this.securityViolations = [];
    this.lastSecurityCheck = null;
    this.hardwareFingerprint = null;
  }

  /**
   * Initialize security service
   */
  async initialize() {
    try {
      console.log('🔒 Initializing security service...');
      
      // Generate hardware fingerprint
      this.hardwareFingerprint = await this.generateHardwareFingerprint();
      
      // Start security monitoring
      this.startSecurityMonitoring();
      
      // Initialize anti-tampering
      this.initializeAntiTampering();
      
      this.isInitialized = true;
      console.log('✅ Security service initialized');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize security service:', error);
      return false;
    }
  }

  /**
   * Generate hardware fingerprint
   */
  async generateHardwareFingerprint() {
    try {
      const components = [];
      
      // Machine ID
      try {
        components.push(machineIdSync());
      } catch (error) {
        console.warn('Could not get machine ID:', error);
      }
      
      // Screen resolution
      if (typeof window !== 'undefined' && window.screen) {
        components.push(`${window.screen.width}x${window.screen.height}`);
        components.push(`${window.screen.colorDepth}`);
      }
      
      // Timezone
      try {
        components.push(Intl.DateTimeFormat().resolvedOptions().timeZone);
      } catch (error) {
        components.push(new Date().getTimezoneOffset().toString());
      }
      
      // Language
      if (typeof navigator !== 'undefined') {
        components.push(navigator.language || navigator.userLanguage || 'unknown');
      }
      
      // Platform
      if (typeof navigator !== 'undefined') {
        components.push(navigator.platform || 'unknown');
      }
      
      // CPU cores (if available)
      if (typeof navigator !== 'undefined' && navigator.hardwareConcurrency) {
        components.push(navigator.hardwareConcurrency.toString());
      }
      
      // Create fingerprint hash
      const fingerprintData = components.join('|');
      const fingerprint = CryptoJS.SHA256(fingerprintData).toString();
      
      console.log('🔍 Hardware fingerprint generated');
      return fingerprint;
      
    } catch (error) {
      console.error('❌ Failed to generate hardware fingerprint:', error);
      return CryptoJS.SHA256('fallback-fingerprint-' + Date.now()).toString();
    }
  }

  /**
   * Initialize anti-tampering detection
   */
  initializeAntiTampering() {
    if (typeof window === 'undefined') return;
    
    // Detect developer tools
    this.detectDevTools();
    
    // Detect debugging attempts
    this.detectDebugging();
    
    // Monitor for suspicious activity
    this.monitorSuspiciousActivity();
    
    console.log('🛡️ Anti-tampering detection initialized');
  }

  /**
   * Detect if developer tools are open
   */
  detectDevTools() {
    if (typeof window === 'undefined') return;
    
    let devtools = {
      open: false,
      orientation: null
    };
    
    const threshold = 160;
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          this.handleSecurityViolation('DevTools detected');
        }
      } else {
        devtools.open = false;
      }
    }, 500);
  }

  /**
   * Detect debugging attempts
   */
  detectDebugging() {
    if (typeof window === 'undefined') return;
    
    // Detect debugger statements
    let start = Date.now();
    debugger;
    let end = Date.now();
    
    if (end - start > 100) {
      this.handleSecurityViolation('Debugger detected');
    }
    
    // Periodic debugging detection
    setInterval(() => {
      let start = Date.now();
      debugger;
      let end = Date.now();
      
      if (end - start > 100) {
        this.handleSecurityViolation('Debugger detected');
      }
    }, 10000);
  }

  /**
   * Monitor for suspicious activity
   */
  monitorSuspiciousActivity() {
    if (typeof window === 'undefined') return;
    
    // Monitor for rapid key combinations (potential automation)
    let keyPressCount = 0;
    let keyPressWindow = Date.now();
    
    document.addEventListener('keydown', (event) => {
      const now = Date.now();
      
      if (now - keyPressWindow < 1000) {
        keyPressCount++;
        if (keyPressCount > 20) {
          this.handleSecurityViolation('Suspicious keyboard activity');
          keyPressCount = 0;
        }
      } else {
        keyPressCount = 1;
        keyPressWindow = now;
      }
    });
    
    // Monitor for suspicious mouse activity
    let mouseClickCount = 0;
    let mouseClickWindow = Date.now();
    
    document.addEventListener('click', () => {
      const now = Date.now();
      
      if (now - mouseClickWindow < 1000) {
        mouseClickCount++;
        if (mouseClickCount > 15) {
          this.handleSecurityViolation('Suspicious mouse activity');
          mouseClickCount = 0;
        }
      } else {
        mouseClickCount = 1;
        mouseClickWindow = now;
      }
    });
  }

  /**
   * Handle security violations
   */
  handleSecurityViolation(violationType) {
    const violation = {
      type: violationType,
      timestamp: new Date().toISOString(),
      fingerprint: this.hardwareFingerprint
    };
    
    this.securityViolations.push(violation);
    
    console.warn('🚨 Security violation detected:', violationType);
    
    // Limit violations array size
    if (this.securityViolations.length > 100) {
      this.securityViolations = this.securityViolations.slice(-50);
    }
    
    // Take action based on violation type
    this.respondToViolation(violation);
  }

  /**
   * Respond to security violations
   */
  respondToViolation(violation) {
    // For now, just log the violation
    // In a production environment, you might want to:
    // - Disable certain features
    // - Log the violation to a server
    // - Show a warning to the user
    // - Temporarily lock the application
    
    console.warn('🔒 Security response triggered for:', violation.type);
  }

  /**
   * Start security monitoring
   */
  startSecurityMonitoring() {
    // Periodic security checks
    setInterval(() => {
      this.performSecurityCheck();
    }, 30000); // Every 30 seconds
    
    console.log('👁️ Security monitoring started');
  }

  /**
   * Perform periodic security check
   */
  performSecurityCheck() {
    try {
      this.lastSecurityCheck = new Date().toISOString();
      
      // Check if hardware fingerprint has changed
      this.generateHardwareFingerprint().then(newFingerprint => {
        if (this.hardwareFingerprint && newFingerprint !== this.hardwareFingerprint) {
          this.handleSecurityViolation('Hardware fingerprint changed');
        }
      });
      
      // Check for suspicious patterns in violations
      this.analyzeViolationPatterns();
      
    } catch (error) {
      console.error('❌ Security check failed:', error);
    }
  }

  /**
   * Analyze violation patterns
   */
  analyzeViolationPatterns() {
    if (this.securityViolations.length < 5) return;
    
    const recentViolations = this.securityViolations.slice(-10);
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);
    
    const recentCount = recentViolations.filter(v => 
      new Date(v.timestamp).getTime() > fiveMinutesAgo
    ).length;
    
    if (recentCount > 5) {
      this.handleSecurityViolation('Multiple violations detected');
    }
  }

  /**
   * Validate session integrity
   */
  validateSession() {
    try {
      const sessionData = localStorage.getItem(this.sessionKey);
      
      if (!sessionData) {
        return this.createNewSession();
      }
      
      const decrypted = CryptoJS.AES.decrypt(sessionData, this.securityKey);
      const session = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
      
      // Check session validity
      const now = Date.now();
      const sessionAge = now - new Date(session.created).getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (sessionAge > maxAge) {
        return this.createNewSession();
      }
      
      // Check fingerprint
      if (session.fingerprint !== this.hardwareFingerprint) {
        this.handleSecurityViolation('Session fingerprint mismatch');
        return this.createNewSession();
      }
      
      return true;
      
    } catch (error) {
      console.error('❌ Session validation failed:', error);
      return this.createNewSession();
    }
  }

  /**
   * Create new security session
   */
  createNewSession() {
    try {
      const session = {
        id: CryptoJS.lib.WordArray.random(16).toString(),
        created: new Date().toISOString(),
        fingerprint: this.hardwareFingerprint,
        violations: 0
      };
      
      const encrypted = CryptoJS.AES.encrypt(
        JSON.stringify(session), 
        this.securityKey
      ).toString();
      
      localStorage.setItem(this.sessionKey, encrypted);
      
      console.log('🔐 New security session created');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to create security session:', error);
      return false;
    }
  }

  /**
   * Get security status
   */
  getSecurityStatus() {
    return {
      isInitialized: this.isInitialized,
      hardwareFingerprint: this.hardwareFingerprint,
      violationCount: this.securityViolations.length,
      lastCheck: this.lastSecurityCheck,
      recentViolations: this.securityViolations.slice(-5)
    };
  }

  /**
   * Clear security violations (for testing)
   */
  clearViolations() {
    this.securityViolations = [];
    console.log('🧹 Security violations cleared');
  }
}

// Create singleton instance
export const securityService = new SecurityService();

// Export default
export default securityService;
