import React, { createContext, useContext, useState, useEffect } from 'react';
import { translations, getTranslation } from './translations.js';

// Language configuration with layout directions
export const LANGUAGES = {
  ar: {
    code: 'ar',
    name: 'العربية',
    direction: 'rtl',
    sidebarPosition: 'right',
    textAlign: 'right'
  },
  fr: {
    code: 'fr',
    name: 'Français',
    direction: 'ltr',
    sidebarPosition: 'left',
    textAlign: 'left'
  },
  en: {
    code: 'en',
    name: 'English',
    direction: 'ltr',
    sidebarPosition: 'left',
    textAlign: 'left'
  }
};

export const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  // Get saved language or detect system language
  const getSavedLanguage = () => {
    try {
      const saved = localStorage.getItem('icaldz-language');
      if (saved && LANGUAGES[saved]) {
        return saved;
      }

      // Try to detect system language
      const browserLang = navigator.language || navigator.userLanguage;
      if (browserLang) {
        if (browserLang.startsWith('ar')) return 'ar';
        if (browserLang.startsWith('fr')) return 'fr';
        if (browserLang.startsWith('en')) return 'en';
      }

      // Default to Arabic
      return 'ar';
    } catch (error) {
      console.error('Error loading saved language:', error);
      return 'ar';
    }
  };

  const [currentLanguage, setCurrentLanguage] = useState(getSavedLanguage());
  const [isLanguageSelected, setIsLanguageSelected] = useState(() => {
    return localStorage.getItem('icaldz-language-selected') === 'true';
  });

  // Save language preference
  const saveLanguage = (langCode) => {
    try {
      localStorage.setItem('icaldz-language', langCode);
      localStorage.setItem('icaldz-language-selected', 'true');
    } catch (error) {
      console.error('Error saving language:', error);
    }
  };

  // Change language function
  const changeLanguage = async (langCode) => {
    if (LANGUAGES[langCode]) {
      setCurrentLanguage(langCode);
      setIsLanguageSelected(true);
      saveLanguage(langCode);

      // Update document direction and class
      updateDocumentDirection(langCode);

      // Notify Electron main process if available
      if (window.require) {
        try {
          const { ipcRenderer } = window.require('electron');
          await ipcRenderer.invoke('set-app-language', langCode);
        } catch (error) {
          console.log('Not running in Electron or IPC not available');
        }
      }

      console.log(`Language changed to: ${LANGUAGES[langCode].name}`);
      return true;
    }
    return false;
  };

  // Update document direction and CSS classes
  const updateDocumentDirection = (langCode) => {
    const language = LANGUAGES[langCode];
    const html = document.documentElement;
    const body = document.body;

    // Set direction
    html.dir = language.direction;
    body.dir = language.direction;

    // Remove old language classes
    Object.keys(LANGUAGES).forEach(lang => {
      html.classList.remove(`lang-${lang}`);
      body.classList.remove(`lang-${lang}`);
    });

    // Add new language class
    html.classList.add(`lang-${langCode}`);
    body.classList.add(`lang-${langCode}`);

    // Set CSS custom properties for layout
    const root = document.documentElement;
    root.style.setProperty('--text-align', language.textAlign);
    root.style.setProperty('--sidebar-position', language.sidebarPosition);
    root.style.setProperty('--floating-position', language.floatingButtonsPosition);
  };

  // Translation function
  const t = (key, fallback = null) => {
    return getTranslation(key, currentLanguage) || fallback || key;
  };

  // Get current language configuration
  const getCurrentLanguageConfig = () => {
    return LANGUAGES[currentLanguage];
  };

  // Initialize document direction on mount
  useEffect(() => {
    updateDocumentDirection(currentLanguage);
  }, [currentLanguage]);

  const value = {
    currentLanguage,
    isLanguageSelected,
    languages: LANGUAGES,
    changeLanguage,
    t,
    getCurrentLanguageConfig,
    setIsLanguageSelected
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
