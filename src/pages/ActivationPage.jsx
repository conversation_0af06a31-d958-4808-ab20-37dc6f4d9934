/**
 * Modern Activation Page Component
 * 
 * A beautiful, modern activation page with multi-language support
 * and responsive design for entering activation codes.
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../LanguageContext.jsx';
import { activationService } from '../services/ActivationService';
import './ActivationPage.css';

const ActivationPage = ({ onActivationSuccess, onClose }) => {
  const { currentLanguage, t } = useLanguage();
  const [activationCode, setActivationCode] = useState('');
  const [userName, setUserName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [deviceId, setDeviceId] = useState('');
  const [step, setStep] = useState(1); // 1: Welcome, 2: Code Entry, 3: Success

  useEffect(() => {
    // Get device ID for display
    const initializePage = async () => {
      await activationService.initialize();
      setDeviceId(activationService.deviceId);
    };
    initializePage();
  }, []);

  // t function is already available from useLanguage hook

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Validate input
      if (!activationCode.trim()) {
        throw new Error(t('activationCodeRequired', 'Activation code is required'));
      }

      // Attempt activation
      const result = await activationService.activate(activationCode.trim(), userName.trim());

      if (result.success) {
        setStep(3); // Success step
        setTimeout(() => {
          if (onActivationSuccess) {
            onActivationSuccess(result.data);
          }
        }, 2000);
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError(error.message || t('activationFailed', 'Activation failed'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatActivationCode = (value) => {
    // Remove any non-alphanumeric characters except hyphens
    const cleaned = value.replace(/[^A-Z0-9-]/gi, '').toUpperCase();
    
    // Add hyphens in the correct positions
    if (cleaned.length <= 4) return cleaned;
    if (cleaned.length <= 8) return `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
    if (cleaned.length <= 13) return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8)}`;
    if (cleaned.length <= 18) return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8, 12)}-${cleaned.slice(12)}`;
    if (cleaned.length <= 23) return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8, 12)}-${cleaned.slice(12, 16)}-${cleaned.slice(16)}`;
    
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 8)}-${cleaned.slice(8, 12)}-${cleaned.slice(12, 16)}-${cleaned.slice(16, 20)}`;
  };

  const handleCodeChange = (e) => {
    const formatted = formatActivationCode(e.target.value);
    setActivationCode(formatted);
  };

  const renderWelcomeStep = () => (
    <div className="activation-step welcome-step">
      <div className="welcome-content">
        <div className="app-logo">
          <img src="/logosvg.svg" alt="iCalDZ" className="logo-image" />
        </div>
        <h1 className="welcome-title">
          {t('welcomeToICalDZ', 'مرحباً بك في iCalDZ')}
        </h1>
        <p className="welcome-subtitle">
          {t('activationWelcomeMessage', 'نظام المحاسبة المتكامل لإدارة أعمالك بكفاءة عالية')}
        </p>
        <div className="features-list">
          <div className="feature-item">
            <span className="feature-icon">📊</span>
            <span className="feature-text">{t('feature1', 'إدارة المبيعات والمشتريات')}</span>
          </div>
          <div className="feature-item">
            <span className="feature-icon">📦</span>
            <span className="feature-text">{t('feature2', 'إدارة المخزون والأصناف')}</span>
          </div>
          <div className="feature-item">
            <span className="feature-icon">📈</span>
            <span className="feature-text">{t('feature3', 'التقارير المالية المتكاملة')}</span>
          </div>
        </div>
        <button 
          className="btn-primary btn-large"
          onClick={() => setStep(2)}
        >
          {t('startActivation', 'بدء التفعيل')}
          <span className="btn-arrow">→</span>
        </button>
      </div>
    </div>
  );

  const renderCodeEntryStep = () => (
    <div className="activation-step code-entry-step">
      <div className="step-header">
        <button 
          className="back-button"
          onClick={() => setStep(1)}
        >
          ← {t('back', 'رجوع')}
        </button>
        <h2 className="step-title">
          {t('enterActivationCode', 'أدخل كود التفعيل')}
        </h2>
      </div>

      <form onSubmit={handleSubmit} className="activation-form">
        <div className="form-section">
          <div className="form-group">
            <label htmlFor="activationCode" className="form-label">
              {t('activationCode', 'كود التفعيل')}
            </label>
            <input
              type="text"
              id="activationCode"
              value={activationCode}
              onChange={handleCodeChange}
              placeholder={t('codeFormatPlaceholder', 'ICAL-2025-XXXX-XXXX-XXXX')}
              className="form-input code-input"
              maxLength={24}
              required
              disabled={isLoading}
              dir="ltr"
            />
            <div className="input-hint">
              {t('supportedFormats', 'الأنواع المدعومة: ICAL (دائم), TRIAL3 (3 أيام), TRIAL7 (7 أيام), TRIAL30 (30 يوم)')}
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="userName" className="form-label">
              {t('userName', 'اسم المستخدم')} 
              <span className="optional">({t('optional', 'اختياري')})</span>
            </label>
            <input
              type="text"
              id="userName"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              placeholder={t('enterUserName', 'أدخل اسم المستخدم')}
              className="form-input"
              disabled={isLoading}
            />
          </div>

          <div className="device-info-card">
            <div className="device-info-header">
              <span className="device-icon">🔒</span>
              <span className="device-title">{t('deviceInformation', 'معلومات الجهاز')}</span>
            </div>
            <div className="device-id-display">
              <span className="device-label">{t('deviceId', 'معرف الجهاز')}:</span>
              <span className="device-value">
                {deviceId ? deviceId.substring(0, 16) + '...' : t('loading', 'جاري التحميل...')}
              </span>
            </div>
            <div className="device-note">
              {t('deviceBindingNote', 'سيتم ربط الكود بهذا الجهاز فقط')}
            </div>
          </div>

          {error && (
            <div className="error-card">
              <div className="error-icon">⚠️</div>
              <div className="error-content">
                <div className="error-title">{t('activationError', 'خطأ في التفعيل')}</div>
                <div className="error-message">{error}</div>
              </div>
            </div>
          )}

          <button
            type="submit"
            className="btn-primary btn-large btn-submit"
            disabled={isLoading || !activationCode.trim()}
          >
            {isLoading ? (
              <>
                <div className="loading-spinner"></div>
                {t('activating', 'جاري التفعيل...')}
              </>
            ) : (
              <>
                {t('activate', 'تفعيل')}
                <span className="btn-arrow">✓</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="activation-step success-step">
      <div className="success-content">
        <div className="success-icon">✅</div>
        <h2 className="success-title">
          {t('activationSuccessful', 'تم التفعيل بنجاح!')}
        </h2>
        <p className="success-message">
          {t('activationSuccessMessage', 'تم تفعيل التطبيق بنجاح. يمكنك الآن الاستمتاع بجميع الميزات.')}
        </p>
        <div className="success-details">
          <div className="detail-item">
            <span className="detail-label">{t('activationCode', 'كود التفعيل')}:</span>
            <span className="detail-value">{activationCode}</span>
          </div>
          {userName && (
            <div className="detail-item">
              <span className="detail-label">{t('userName', 'اسم المستخدم')}:</span>
              <span className="detail-value">{userName}</span>
            </div>
          )}
        </div>
        <div className="loading-progress">
          <div className="progress-bar">
            <div className="progress-fill"></div>
          </div>
          <p className="progress-text">
            {t('redirectingToApp', 'جاري توجيهك إلى التطبيق...')}
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="activation-page" dir={currentLanguage === 'ar' ? 'rtl' : 'ltr'}>
      <div className="activation-container">
        <div className="activation-card">
          {step === 1 && renderWelcomeStep()}
          {step === 2 && renderCodeEntryStep()}
          {step === 3 && renderSuccessStep()}
        </div>
        
        <div className="activation-footer">
          <div className="support-section">
            <p className="support-text">
              {t('needHelp', 'تحتاج مساعدة؟')} 
              <a href="mailto:<EMAIL>" className="support-link">
                {t('contactSupport', 'تواصل مع الدعم الفني')}
              </a>
            </p>
          </div>
          <div className="branding">
            <p>{t('developedBy', 'تم التطوير بواسطة')} <strong>iCode DZ</strong></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivationPage;
