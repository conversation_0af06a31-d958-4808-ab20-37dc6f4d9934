@echo off
REM iCalDZ Activation Code Generator - Windows Batch File
REM This script provides an easy interface for generating activation codes

title iCalDZ Code Generator

echo.
echo ========================================
echo    iCalDZ Activation Code Generator
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if the generator script exists
if not exist "%~dp0generateActivationCodes.js" (
    echo ERROR: generateActivationCodes.js not found
    echo Please ensure the script is in the same directory as this batch file
    pause
    exit /b 1
)

:MENU
echo.
echo Select an option:
echo.
echo 1. Generate 10 lifetime codes
echo 2. Generate 50 lifetime codes
echo 3. Generate 100 lifetime codes
echo 4. Generate 3-day trial codes
echo 5. Generate 7-day trial codes
echo 6. Generate 30-day trial codes
echo 7. Custom generation
echo 8. Import codes from CSV
echo 9. Export codes to CSV
echo 10. View help
echo 11. Exit
echo.
set /p choice="Enter your choice (1-11): "

if "%choice%"=="1" goto LIFETIME_10
if "%choice%"=="2" goto LIFETIME_50
if "%choice%"=="3" goto LIFETIME_100
if "%choice%"=="4" goto TRIAL_3
if "%choice%"=="5" goto TRIAL_7
if "%choice%"=="6" goto TRIAL_30
if "%choice%"=="7" goto CUSTOM
if "%choice%"=="8" goto IMPORT_CSV
if "%choice%"=="9" goto EXPORT_CSV
if "%choice%"=="10" goto HELP
if "%choice%"=="11" goto EXIT

echo Invalid choice. Please try again.
goto MENU

:LIFETIME_10
echo.
echo Generating 10 lifetime activation codes...
node "%~dp0generateActivationCodes.js" --count 10 --type lifetime
goto CONTINUE

:LIFETIME_50
echo.
echo Generating 50 lifetime activation codes...
node "%~dp0generateActivationCodes.js" --count 50 --type lifetime
goto CONTINUE

:LIFETIME_100
echo.
echo Generating 100 lifetime activation codes...
node "%~dp0generateActivationCodes.js" --count 100 --type lifetime
goto CONTINUE

:TRIAL_3
echo.
set /p trial_count="Enter number of 3-day trial codes to generate (default 10): "
if "%trial_count%"=="" set trial_count=10
echo Generating %trial_count% 3-day trial codes...
node "%~dp0generateActivationCodes.js" --count %trial_count% --type trial-3
goto CONTINUE

:TRIAL_7
echo.
set /p trial_count="Enter number of 7-day trial codes to generate (default 10): "
if "%trial_count%"=="" set trial_count=10
echo Generating %trial_count% 7-day trial codes...
node "%~dp0generateActivationCodes.js" --count %trial_count% --type trial-7
goto CONTINUE

:TRIAL_30
echo.
set /p trial_count="Enter number of 30-day trial codes to generate (default 10): "
if "%trial_count%"=="" set trial_count=10
echo Generating %trial_count% 30-day trial codes...
node "%~dp0generateActivationCodes.js" --count %trial_count% --type trial-30
goto CONTINUE

:CUSTOM
echo.
echo Custom Code Generation
echo =====================
echo.
set /p custom_count="Enter number of codes (1-1000): "
echo.
echo Code types:
echo 1. Lifetime (permanent)
echo 2. 3-day trial
echo 3. 7-day trial
echo 4. 30-day trial
echo.
set /p type_choice="Select code type (1-4): "

if "%type_choice%"=="1" set custom_type=lifetime
if "%type_choice%"=="2" set custom_type=trial-3
if "%type_choice%"=="3" set custom_type=trial-7
if "%type_choice%"=="4" set custom_type=trial-30

if "%custom_type%"=="" (
    echo Invalid type selection.
    goto CUSTOM
)

echo.
set /p batch_name="Enter batch name (optional): "
set /p output_file="Enter output filename (optional): "

echo.
echo Generating %custom_count% %custom_type% codes...

set cmd_args=--count %custom_count% --type %custom_type%
if not "%batch_name%"=="" set cmd_args=%cmd_args% --batch-name "%batch_name%"
if not "%output_file%"=="" set cmd_args=%cmd_args% --output "%output_file%"

node "%~dp0generateActivationCodes.js" %cmd_args%
goto CONTINUE

:IMPORT_CSV
echo.
echo CSV Import Tool
echo ===============
echo.
set /p csv_file="Enter CSV file path: "
if "%csv_file%"=="" (
    echo No file specified.
    goto CONTINUE
)
echo.
echo Importing codes from %csv_file%...
node "%~dp0importCodesFromCSV.js" "%csv_file%"
goto CONTINUE

:EXPORT_CSV
echo.
echo CSV Export Tool
echo ===============
echo.
echo Export options:
echo 1. All codes
echo 2. Unused codes only
echo 3. Activated codes only
echo 4. Custom export
echo.
set /p export_choice="Select export option (1-4): "

if "%export_choice%"=="1" (
    echo Exporting all codes...
    node "%~dp0exportCodesFromSupabase.js"
) else if "%export_choice%"=="2" (
    echo Exporting unused codes...
    node "%~dp0exportCodesFromSupabase.js" --status unused
) else if "%export_choice%"=="3" (
    echo Exporting activated codes...
    node "%~dp0exportCodesFromSupabase.js" --status activated
) else if "%export_choice%"=="4" (
    echo Custom export options:
    set /p export_status="Status (unused/activated/blocked/all): "
    set /p export_type="Type (lifetime/trial-3/trial-7/trial-30/all): "
    set /p export_limit="Limit (number or leave empty): "
    set /p export_file="Output file (or leave empty): "

    set export_args=
    if not "%export_status%"=="" set export_args=%export_args% --status "%export_status%"
    if not "%export_type%"=="" set export_args=%export_args% --type "%export_type%"
    if not "%export_limit%"=="" set export_args=%export_args% --limit %export_limit%
    if not "%export_file%"=="" set export_args=%export_args% --output "%export_file%"

    echo Exporting with custom options...
    node "%~dp0exportCodesFromSupabase.js" %export_args%
) else (
    echo Invalid choice.
)
goto CONTINUE

:HELP
echo.
node "%~dp0generateActivationCodes.js" --help
goto CONTINUE

:CONTINUE
echo.
echo ========================================
set /p continue_choice="Do you want to generate more codes? (y/n): "
if /i "%continue_choice%"=="y" goto MENU
if /i "%continue_choice%"=="yes" goto MENU

:EXIT
echo.
echo Thank you for using iCalDZ Code Generator!
echo.
pause
exit /b 0
