#!/usr/bin/env node

/**
 * iCalDZ Activation Code CSV Import Tool
 * 
 * This tool imports activation codes from a CSV file into Supabase.
 * It supports bulk import and validation of codes.
 * 
 * CSV Format:
 * activation_code,user_name,status
 * ICAL-2025-ABCD-EFGH-IJKL,User Name,unused
 * TRIAL7-2025-XYZ1-ABC2-DEF3,Trial User,unused
 * 
 * Usage:
 *   node importCodesFromCSV.js <csv-file> [options]
 * 
 * Options:
 *   --validate-only    Only validate codes without importing
 *   --batch-size <n>   Import in batches of n codes (default: 100)
 *   --help             Show help
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const SUPABASE_URL = 'https://meaorrtisoruuoupldwq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYW9ycnRpc29ydXVvdXBsZHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNzQ3ODgsImV4cCI6MjA2Nzg1MDc4OH0.YERbS4mDoNhaxvF5fHNoQfE4bjvaxqptcN-11cSZjHM';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

function isValidCodeFormat(code) {
  if (!code || typeof code !== 'string') return false;
  
  const patterns = [
    /^ICAL-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
    /^TRIAL3-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
    /^TRIAL7-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
    /^TRIAL30-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i
  ];
  
  return patterns.some(pattern => pattern.test(code.trim().toUpperCase()));
}

function parseCSV(csvContent) {
  const lines = csvContent.split('\n').filter(line => line.trim());
  
  if (lines.length === 0) {
    throw new Error('CSV file is empty');
  }
  
  // Parse header
  const header = lines[0].split(',').map(col => col.trim().toLowerCase());
  const expectedColumns = ['activation_code', 'user_name', 'status'];
  
  // Validate header
  const hasRequiredColumns = expectedColumns.every(col => 
    header.includes(col) || header.includes(col.replace('_', ''))
  );
  
  if (!hasRequiredColumns) {
    throw new Error(`CSV must have columns: ${expectedColumns.join(', ')}`);
  }
  
  // Find column indices
  const codeIndex = header.findIndex(col => 
    col === 'activation_code' || col === 'activationcode' || col === 'code'
  );
  const userIndex = header.findIndex(col => 
    col === 'user_name' || col === 'username' || col === 'user'
  );
  const statusIndex = header.findIndex(col => 
    col === 'status'
  );
  
  // Parse data rows
  const codes = [];
  const errors = [];
  
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line.trim()) continue;
    
    const columns = line.split(',').map(col => col.trim());
    
    if (columns.length < Math.max(codeIndex, userIndex, statusIndex) + 1) {
      errors.push(`Line ${i + 1}: Not enough columns`);
      continue;
    }
    
    const activationCode = columns[codeIndex]?.replace(/"/g, '').trim().toUpperCase();
    const userName = columns[userIndex]?.replace(/"/g, '').trim() || null;
    const status = columns[statusIndex]?.replace(/"/g, '').trim().toLowerCase() || 'unused';
    
    // Validate code format
    if (!isValidCodeFormat(activationCode)) {
      errors.push(`Line ${i + 1}: Invalid code format: ${activationCode}`);
      continue;
    }
    
    // Validate status
    if (!['unused', 'activated', 'blocked'].includes(status)) {
      errors.push(`Line ${i + 1}: Invalid status: ${status}. Must be: unused, activated, or blocked`);
      continue;
    }
    
    codes.push({
      activation_code: activationCode,
      user_name: userName,
      status: status,
      created_at: new Date().toISOString()
    });
  }
  
  return { codes, errors };
}

async function validateCodesInDatabase(codes) {
  console.log('🔍 Checking for existing codes in database...');
  
  const existingCodes = [];
  const batchSize = 50;
  
  for (let i = 0; i < codes.length; i += batchSize) {
    const batch = codes.slice(i, i + batchSize);
    const codesToCheck = batch.map(code => code.activation_code);
    
    const { data, error } = await supabase
      .from('activations')
      .select('activation_code')
      .in('activation_code', codesToCheck);
    
    if (error) {
      console.error('❌ Error checking existing codes:', error);
      throw error;
    }
    
    if (data && data.length > 0) {
      existingCodes.push(...data.map(row => row.activation_code));
    }
  }
  
  return existingCodes;
}

async function importCodes(codes, batchSize = 100) {
  console.log(`📥 Importing ${codes.length} codes in batches of ${batchSize}...`);
  
  let imported = 0;
  let failed = 0;
  const errors = [];
  
  for (let i = 0; i < codes.length; i += batchSize) {
    const batch = codes.slice(i, i + batchSize);
    
    try {
      const { data, error } = await supabase
        .from('activations')
        .insert(batch);
      
      if (error) {
        console.error(`❌ Batch ${Math.floor(i / batchSize) + 1} failed:`, error);
        errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
        failed += batch.length;
      } else {
        imported += batch.length;
        console.log(`✅ Batch ${Math.floor(i / batchSize) + 1}: ${batch.length} codes imported`);
      }
    } catch (error) {
      console.error(`❌ Batch ${Math.floor(i / batchSize) + 1} error:`, error);
      errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
      failed += batch.length;
    }
  }
  
  return { imported, failed, errors };
}

function showHelp() {
  console.log(`
📥 iCalDZ Activation Code CSV Import Tool

Usage:
  node importCodesFromCSV.js <csv-file> [options]

Options:
  --validate-only      Only validate codes without importing
  --batch-size <n>     Import in batches of n codes (default: 100)
  --help              Show this help message

CSV Format:
  The CSV file should have the following columns:
  - activation_code: The activation code (required)
  - user_name: User or batch name (optional)
  - status: Code status - unused, activated, or blocked (default: unused)

Example CSV:
  activation_code,user_name,status
  ICAL-2025-ABCD-EFGH-IJKL,John Doe,unused
  TRIAL7-2025-XYZ1-ABC2-DEF3,Trial User,unused
  TRIAL30-2025-TEST-CODE-HERE,Beta Tester,unused

Supported Code Formats:
  - ICAL-YYYY-XXXX-XXXX-XXXX (lifetime codes)
  - TRIAL3-YYYY-XXXX-XXXX-XXXX (3-day trial codes)
  - TRIAL7-YYYY-XXXX-XXXX-XXXX (7-day trial codes)
  - TRIAL30-YYYY-XXXX-XXXX-XXXX (30-day trial codes)

Examples:
  node importCodesFromCSV.js codes.csv
  node importCodesFromCSV.js codes.csv --validate-only
  node importCodesFromCSV.js codes.csv --batch-size 50
`);
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help')) {
    showHelp();
    return;
  }
  
  const csvFile = args[0];
  const validateOnly = args.includes('--validate-only');
  let batchSize = 100;
  
  // Parse batch size
  const batchSizeIndex = args.indexOf('--batch-size');
  if (batchSizeIndex !== -1 && args[batchSizeIndex + 1]) {
    batchSize = parseInt(args[batchSizeIndex + 1]) || 100;
  }
  
  try {
    // Check if file exists
    if (!fs.existsSync(csvFile)) {
      console.error(`❌ File not found: ${csvFile}`);
      return;
    }
    
    console.log('📄 Reading CSV file...');
    const csvContent = fs.readFileSync(csvFile, 'utf8');
    
    console.log('🔍 Parsing CSV content...');
    const { codes, errors } = parseCSV(csvContent);
    
    // Show parsing results
    console.log(`\n📊 Parsing Results:`);
    console.log(`✅ Valid codes: ${codes.length}`);
    console.log(`❌ Errors: ${errors.length}`);
    
    if (errors.length > 0) {
      console.log('\n❌ Parsing Errors:');
      errors.forEach(error => console.log(`   ${error}`));
    }
    
    if (codes.length === 0) {
      console.log('❌ No valid codes found to import');
      return;
    }
    
    // Check for existing codes
    const existingCodes = await validateCodesInDatabase(codes);
    
    if (existingCodes.length > 0) {
      console.log(`\n⚠️ Found ${existingCodes.length} codes that already exist:`);
      existingCodes.forEach(code => console.log(`   ${code}`));
      
      // Filter out existing codes
      const newCodes = codes.filter(code => 
        !existingCodes.includes(code.activation_code)
      );
      
      console.log(`\n📋 Codes to import: ${newCodes.length}`);
      
      if (newCodes.length === 0) {
        console.log('❌ All codes already exist in database');
        return;
      }
      
      codes.length = 0;
      codes.push(...newCodes);
    }
    
    if (validateOnly) {
      console.log('\n✅ Validation completed. Use without --validate-only to import.');
      return;
    }
    
    // Import codes
    console.log(`\n🚀 Starting import of ${codes.length} codes...`);
    const result = await importCodes(codes, batchSize);
    
    // Show results
    console.log('\n🎉 Import completed!');
    console.log(`✅ Successfully imported: ${result.imported} codes`);
    console.log(`❌ Failed to import: ${result.failed} codes`);
    
    if (result.errors.length > 0) {
      console.log('\n❌ Import Errors:');
      result.errors.forEach(error => console.log(`   ${error}`));
    }
    
  } catch (error) {
    console.error('❌ Error during import:', error.message);
    process.exit(1);
  }
}

// Run the script if this file is executed directly
if (process.argv[1] && import.meta.url.endsWith('importCodesFromCSV.js')) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
