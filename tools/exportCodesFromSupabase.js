#!/usr/bin/env node

/**
 * iCalDZ Activation Code Export Tool
 * 
 * This tool exports activation codes from Supabase to CSV format.
 * Useful for backing up codes or creating distribution lists.
 * 
 * Usage:
 *   node exportCodesFromSupabase.js [options]
 * 
 * Options:
 *   --status <status>    Export codes with specific status (unused, activated, blocked, all)
 *   --type <type>        Export specific code type (lifetime, trial-3, trial-7, trial-30, all)
 *   --output <file>      Output CSV file (default: codes-export-TIMESTAMP.csv)
 *   --limit <number>     Limit number of codes to export (default: all)
 *   --help               Show help
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const SUPABASE_URL = 'https://meaorrtisoruuoupldwq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYW9ycnRpc29ydXVvdXBsZHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNzQ3ODgsImV4cCI6MjA2Nzg1MDc4OH0.YERbS4mDoNhaxvF5fHNoQfE4bjvaxqptcN-11cSZjHM';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

function getCodeType(code) {
  const upperCode = code.trim().toUpperCase();
  
  if (upperCode.startsWith('ICAL-')) return 'lifetime';
  if (upperCode.startsWith('TRIAL3-')) return 'trial-3';
  if (upperCode.startsWith('TRIAL7-')) return 'trial-7';
  if (upperCode.startsWith('TRIAL30-')) return 'trial-30';
  
  return 'unknown';
}

async function exportCodes(options = {}) {
  const {
    status = 'all',
    type = 'all',
    limit = null,
    outputFile = null
  } = options;

  console.log('🔍 Fetching codes from Supabase...');
  
  // Build query
  let query = supabase
    .from('activations')
    .select('*')
    .order('created_at', { ascending: false });

  // Apply status filter
  if (status !== 'all') {
    query = query.eq('status', status);
  }

  // Apply limit
  if (limit && limit > 0) {
    query = query.limit(limit);
  }

  const { data, error } = await query;

  if (error) {
    console.error('❌ Error fetching codes:', error);
    throw error;
  }

  if (!data || data.length === 0) {
    console.log('❌ No codes found matching the criteria');
    return;
  }

  console.log(`✅ Found ${data.length} codes`);

  // Apply type filter (client-side since it's based on code prefix)
  let filteredData = data;
  if (type !== 'all') {
    filteredData = data.filter(code => getCodeType(code.activation_code) === type);
    console.log(`🔍 Filtered to ${filteredData.length} codes of type: ${type}`);
  }

  if (filteredData.length === 0) {
    console.log('❌ No codes found after filtering');
    return;
  }

  // Generate CSV content
  console.log('📝 Generating CSV content...');
  
  const headers = [
    'activation_code',
    'user_name',
    'status',
    'device_id',
    'activated_at',
    'created_at',
    'code_type'
  ];

  let csvContent = headers.join(',') + '\n';

  filteredData.forEach(code => {
    const row = [
      `"${code.activation_code}"`,
      `"${code.user_name || ''}"`,
      `"${code.status}"`,
      `"${code.device_id || ''}"`,
      `"${code.activated_at || ''}"`,
      `"${code.created_at || ''}"`,
      `"${getCodeType(code.activation_code)}"`
    ];
    csvContent += row.join(',') + '\n';
  });

  // Generate filename
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  const filename = outputFile || `codes-export-${status}-${type}-${timestamp}.csv`;
  const filepath = path.resolve(filename);

  // Write file
  console.log('💾 Writing CSV file...');
  fs.writeFileSync(filepath, csvContent, 'utf8');

  console.log(`✅ Export completed!`);
  console.log(`📄 File: ${filepath}`);
  console.log(`📊 Records: ${filteredData.length}`);
  
  // Show summary
  const summary = {};
  filteredData.forEach(code => {
    const codeType = getCodeType(code.activation_code);
    const status = code.status;
    const key = `${codeType}-${status}`;
    summary[key] = (summary[key] || 0) + 1;
  });

  console.log('\n📈 Export Summary:');
  Object.entries(summary).forEach(([key, count]) => {
    console.log(`   ${key}: ${count} codes`);
  });

  return filepath;
}

function showHelp() {
  console.log(`
📤 iCalDZ Activation Code Export Tool

Usage:
  node exportCodesFromSupabase.js [options]

Options:
  --status <status>    Export codes with specific status
                       Values: unused, activated, blocked, all (default: all)
  
  --type <type>        Export specific code type
                       Values: lifetime, trial-3, trial-7, trial-30, all (default: all)
  
  --output <file>      Output CSV file name
                       Default: codes-export-STATUS-TYPE-DATE.csv
  
  --limit <number>     Limit number of codes to export
                       Default: export all matching codes
  
  --help              Show this help message

Examples:
  # Export all codes
  node exportCodesFromSupabase.js

  # Export only unused lifetime codes
  node exportCodesFromSupabase.js --status unused --type lifetime

  # Export first 100 activated codes
  node exportCodesFromSupabase.js --status activated --limit 100

  # Export trial codes to specific file
  node exportCodesFromSupabase.js --type trial-7 --output "trial-codes.csv"

  # Export blocked codes for review
  node exportCodesFromSupabase.js --status blocked --output "blocked-codes.csv"

CSV Output Format:
  The exported CSV will contain the following columns:
  - activation_code: The activation code
  - user_name: Associated user or batch name
  - status: Current status (unused/activated/blocked)
  - device_id: Device ID if activated
  - activated_at: Activation timestamp
  - created_at: Creation timestamp
  - code_type: Derived code type (lifetime/trial-3/trial-7/trial-30)
`);
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    showHelp();
    return;
  }

  // Parse arguments
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const flag = args[i];
    const value = args[i + 1];
    
    switch (flag) {
      case '--status':
        if (['unused', 'activated', 'blocked', 'all'].includes(value)) {
          options.status = value;
        } else {
          console.error('❌ Invalid status. Use: unused, activated, blocked, or all');
          return;
        }
        break;
      case '--type':
        if (['lifetime', 'trial-3', 'trial-7', 'trial-30', 'all'].includes(value)) {
          options.type = value;
        } else {
          console.error('❌ Invalid type. Use: lifetime, trial-3, trial-7, trial-30, or all');
          return;
        }
        break;
      case '--output':
        options.outputFile = value;
        break;
      case '--limit':
        const limit = parseInt(value);
        if (limit > 0) {
          options.limit = limit;
        } else {
          console.error('❌ Invalid limit. Must be a positive number');
          return;
        }
        break;
      default:
        if (flag.startsWith('--')) {
          console.error(`❌ Unknown option: ${flag}`);
          return;
        }
        break;
    }
  }

  try {
    console.log('🚀 Starting export...');
    console.log(`📋 Options:`, options);
    
    await exportCodes(options);
    
  } catch (error) {
    console.error('❌ Export failed:', error.message);
    process.exit(1);
  }
}

// Run the script if this file is executed directly
if (process.argv[1] && import.meta.url.endsWith('exportCodesFromSupabase.js')) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
