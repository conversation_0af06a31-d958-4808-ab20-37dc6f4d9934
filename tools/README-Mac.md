# iCalDZ Code Generator - macOS Version

## 🍎 Mac Applications

This directory contains multiple ways to run the iCalDZ Code Generator on macOS:

### 1. 📱 Mac App Bundle: `iCalDZ Code Generator.app`
- **Double-click to run** - Works like any Mac application
- **Drag & Drop support** - Drop CSV files for import
- **Finder integration** - Shows up in Applications folder
- **Native macOS experience** - Proper app bundle with icon

**How to use:**
1. Double-click `iCalDZ Code Generator.app`
2. Terminal will open with the interactive menu
3. Follow the on-screen prompts

### 2. 🖥️ Terminal Command: `iCalDZ-Code-Generator.command`
- **Double-click to run** - Opens in Terminal automatically
- **Full-featured** - All import/export/generation features
- **Colored output** - Beautiful terminal interface
- **Interactive menus** - Easy to navigate

**How to use:**
1. Double-click `iCalDZ-Code-Generator.command`
2. Terminal opens automatically
3. Use the interactive menu

### 3. 🔧 Shell Script: `generate-codes.sh`
- **Command line** - Run from Terminal manually
- **Advanced users** - Full control over execution
- **Scriptable** - Can be integrated into workflows

**How to use:**
```bash
cd tools
./generate-codes.sh
```

## 📋 Requirements

- **macOS 10.12** or later
- **Node.js** installed (download from https://nodejs.org/)
- **Terminal access** (built into macOS)

## 🚀 Quick Start

1. **Install Node.js** if not already installed:
   - Visit https://nodejs.org/
   - Download and install the LTS version

2. **Choose your preferred method:**
   - **Easiest**: Double-click `iCalDZ Code Generator.app`
   - **Alternative**: Double-click `iCalDZ-Code-Generator.command`
   - **Advanced**: Run `./generate-codes.sh` in Terminal

3. **Follow the interactive menu** to:
   - Generate activation codes
   - Import codes from CSV
   - Export codes to CSV
   - Validate existing codes

## 📊 Features

### Code Generation
- **Lifetime codes**: `ICAL-2025-XXXX-XXXX-XXXX`
- **3-day trials**: `TRIAL3-2025-XXXX-XXXX-XXXX`
- **7-day trials**: `TRIAL7-2025-XXXX-XXXX-XXXX`
- **30-day trials**: `TRIAL30-2025-XXXX-XXXX-XXXX`

### CSV Management
- **Import**: Bulk import codes from CSV files
- **Export**: Export codes with filtering options
- **Validation**: Check code format and database status

### Interactive Features
- **Drag & Drop**: Drop CSV files onto the app
- **Batch Operations**: Generate thousands of codes
- **Real-time Validation**: Instant feedback
- **Progress Tracking**: See import/export progress

## 🔧 Troubleshooting

### "Node.js not found" Error
1. Install Node.js from https://nodejs.org/
2. Restart Terminal
3. Try running the app again

### "Permission denied" Error
```bash
chmod +x iCalDZ-Code-Generator.command
chmod +x generate-codes.sh
```

### "App can't be opened" Error
1. Right-click the app
2. Select "Open"
3. Click "Open" in the security dialog

### Scripts not found
Make sure all files are in the same `tools` directory:
- `generateActivationCodes.js`
- `importCodesFromCSV.js`
- `exportCodesFromSupabase.js`
- `validateActivationCode.js`

## 📁 File Structure

```
tools/
├── iCalDZ Code Generator.app/     # Mac application bundle
│   ├── Contents/
│   │   ├── Info.plist            # App metadata
│   │   └── MacOS/
│   │       └── iCalDZ Code Generator  # Main executable
├── iCalDZ-Code-Generator.command  # Double-click Terminal app
├── generate-codes.sh              # Shell script
├── generate-codes.bat             # Windows batch file
├── generateActivationCodes.js     # Code generation script
├── importCodesFromCSV.js          # CSV import tool
├── exportCodesFromSupabase.js     # CSV export tool
├── validateActivationCode.js      # Code validation tool
└── sample-codes.csv               # Example CSV format
```

## 🎯 Usage Examples

### Generate 50 Lifetime Codes
1. Open the app
2. Select option 2
3. Codes are automatically saved to Supabase

### Import Codes from CSV
1. Open the app
2. Select option 8
3. Enter the path to your CSV file
4. Or drag & drop the file

### Export Unused Codes
1. Open the app
2. Select option 9
3. Choose "Unused codes only"
4. File is saved to the tools directory

## 🔒 Security Notes

- **Local Processing**: All operations run locally on your Mac
- **Secure Connection**: Uses HTTPS for Supabase communication
- **No Data Storage**: No sensitive data stored on your Mac
- **Open Source**: All scripts are readable and auditable

## 📞 Support

For help or questions:
- **Email**: <EMAIL>
- **Documentation**: See `supabase/supabaseai.md`
- **Validation**: Use the built-in code validator

---

**Developed by iCode DZ** - Professional activation system for iCalDZ accounting software.
