#!/bin/bash

# iCalDZ Code Generator - Mac Application Bundle
# This is the main executable for the macOS application bundle

# Get the directory where this app bundle is located
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../" && pwd)"

# Change to the tools directory
cd "$APP_DIR"

# Check if we're in the right directory
if [ ! -f "generateActivationCodes.js" ]; then
    osascript -e 'display alert "Error" message "iCalDZ Code Generator scripts not found. Please ensure this app is in the tools directory." as critical'
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    osascript -e 'display alert "Node.js Required" message "Node.js is not installed. Please install Node.js from https://nodejs.org/ and try again." as critical'
    open "https://nodejs.org/"
    exit 1
fi

# Check if Terminal is available and run the command script
if [ -f "iCalDZ-Code-Generator.command" ]; then
    # Make sure the command file is executable
    chmod +x "iCalDZ-Code-Generator.command"
    
    # Open Terminal and run the command script
    osascript -e "tell application \"Terminal\" to do script \"cd '$APP_DIR' && ./iCalDZ-Code-Generator.command\""
else
    osascript -e 'display alert "Error" message "iCalDZ-Code-Generator.command not found." as critical'
    exit 1
fi
