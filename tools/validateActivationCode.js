#!/usr/bin/env node

/**
 * iCalDZ Activation Code Validator
 * 
 * This tool validates activation codes and checks their status in Supabase.
 * 
 * Usage:
 *   node validateActivationCode.js <activation-code>
 *   node validateActivationCode.js --interactive
 */

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://meaorrtisoruuoupldwq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYW9ycnRpc29ydXVvdXBsZHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNzQ3ODgsImV4cCI6MjA2Nzg1MDc4OH0.YERbS4mDoNhaxvF5fHNoQfE4bjvaxqptcN-11cSZjHM';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

function isValidCodeFormat(code) {
  if (!code || typeof code !== 'string') return false;
  
  // Expected formats:
  // ICAL-YYYY-XXXX-XXXX-XXXX (lifetime)
  // TRIAL3-YYYY-XXXX-XXXX-XXXX (3-day trial)
  // TRIAL7-YYYY-XXXX-XXXX-XXXX (7-day trial)
  // TRIAL30-YYYY-XXXX-XXXX-XXXX (30-day trial)
  const patterns = [
    /^ICAL-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
    /^TRIAL3-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
    /^TRIAL7-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i,
    /^TRIAL30-\d{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/i
  ];
  
  return patterns.some(pattern => pattern.test(code.trim().toUpperCase()));
}

function getCodeType(code) {
  const upperCode = code.trim().toUpperCase();

  if (upperCode.startsWith('ICAL-')) return 'Lifetime';
  if (upperCode.startsWith('TRIAL3-')) return '3-Day Trial';
  if (upperCode.startsWith('TRIAL7-')) return '7-Day Trial';
  if (upperCode.startsWith('TRIAL30-')) return '30-Day Trial';

  return 'Unknown';
}

async function validateCode(activationCode) {
  console.log(`🔍 Validating activation code: ${activationCode}`);
  console.log('');
  
  // Check format
  if (!isValidCodeFormat(activationCode)) {
    console.log('❌ Invalid code format');
    console.log('Expected format: ICAL-YYYY-XXXX-XXXX-XXXX');
    console.log('                 TRIAL3-YYYY-XXXX-XXXX-XXXX');
    console.log('                 TRIAL7-YYYY-XXXX-XXXX-XXXX');
    console.log('                 TRIAL30-YYYY-XXXX-XXXX-XXXX');
    return false;
  }
  
  console.log('✅ Code format is valid');
  console.log(`📋 Code type: ${getCodeType(activationCode)}`);
  console.log('');
  
  try {
    // Query Supabase
    console.log('🔄 Checking code in database...');
    
    const { data, error } = await supabase
      .from('activations')
      .select('*')
      .eq('activation_code', activationCode.trim().toUpperCase())
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        console.log('❌ Code not found in database');
        return false;
      } else {
        console.error('❌ Database error:', error.message);
        return false;
      }
    }
    
    if (!data) {
      console.log('❌ Code not found in database');
      return false;
    }
    
    // Display code information
    console.log('✅ Code found in database');
    console.log('');
    console.log('📊 Code Information:');
    console.log('==================');
    console.log(`Code: ${data.activation_code}`);
    console.log(`Status: ${data.status}`);
    console.log(`Created: ${data.created_at ? new Date(data.created_at).toLocaleString() : 'Unknown'}`);
    
    if (data.device_id) {
      console.log(`Device ID: ${data.device_id.substring(0, 16)}...`);
    }
    
    if (data.activated_at) {
      console.log(`Activated: ${new Date(data.activated_at).toLocaleString()}`);
    }
    
    if (data.user_name) {
      console.log(`User/Batch: ${data.user_name}`);
    }
    
    console.log('');
    
    // Status-specific information
    switch (data.status) {
      case 'unused':
        console.log('🟢 Status: Available for activation');
        break;
      case 'activated':
        console.log('🟡 Status: Already activated');
        if (data.device_id) {
          console.log(`   Bound to device: ${data.device_id.substring(0, 16)}...`);
        }
        break;
      case 'blocked':
        console.log('🔴 Status: Blocked (cannot be used)');
        break;
      default:
        console.log(`🟠 Status: ${data.status} (unknown status)`);
        break;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Error validating code:', error.message);
    return false;
  }
}

function showHelp() {
  console.log(`
🔍 iCalDZ Activation Code Validator

Usage:
  node validateActivationCode.js <activation-code>
  node validateActivationCode.js --interactive
  node validateActivationCode.js --help

Examples:
  node validateActivationCode.js ICAL-2025-ABCD-EFGH-IJKL
  node validateActivationCode.js TRIAL3-2025-XYZ1-ABC2-DEF3
  node validateActivationCode.js TRIAL7-2025-XYZ1-ABC2-DEF3
  node validateActivationCode.js --interactive

Options:
  --interactive    Interactive mode (prompts for code input)
  --help          Show this help message

This tool will:
- Validate the code format
- Check if the code exists in the database
- Display the code's current status and information
- Show activation history if applicable
`);
}

async function interactiveMode() {
  const readline = await import('readline');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const question = (prompt) => {
    return new Promise((resolve) => {
      rl.question(prompt, resolve);
    });
  };
  
  console.log('🔍 Interactive Activation Code Validator');
  console.log('========================================');
  console.log('');
  
  while (true) {
    const code = await question('Enter activation code (or "exit" to quit): ');
    
    if (code.toLowerCase() === 'exit') {
      break;
    }
    
    if (!code.trim()) {
      console.log('Please enter a valid activation code.');
      continue;
    }
    
    console.log('');
    await validateCode(code.trim());
    console.log('');
    console.log('========================================');
    console.log('');
  }
  
  rl.close();
  console.log('Thank you for using the code validator!');
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('❌ No activation code provided');
    console.log('Use --help for usage information');
    return;
  }
  
  if (args[0] === '--help') {
    showHelp();
    return;
  }
  
  if (args[0] === '--interactive') {
    await interactiveMode();
    return;
  }
  
  const activationCode = args[0];
  await validateCode(activationCode);
}

// Run the script if this file is executed directly
if (process.argv[1] && import.meta.url.endsWith('validateActivationCode.js')) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
