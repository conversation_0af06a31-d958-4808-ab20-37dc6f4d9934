#!/bin/bash

# iCalDZ Activation Code Generator - Mac Terminal Application
# This script provides an easy interface for generating activation codes on macOS
# Double-click this file to run it in Terminal

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the script directory
cd "$SCRIPT_DIR"

# Clear screen and show header
clear
echo -e "${CYAN}"
echo "========================================"
echo "   iCalDZ Activation Code Generator"
echo "        macOS Terminal Application"
echo "========================================"
echo -e "${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}ERROR: Node.js is not installed${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

# Check if the generator script exists
if [ ! -f "$SCRIPT_DIR/generateActivationCodes.js" ]; then
    echo -e "${RED}ERROR: generateActivationCodes.js not found${NC}"
    echo "Please ensure the script is in the same directory as this application"
    echo "Press any key to exit..."
    read -n 1
    exit 1
fi

show_menu() {
    echo
    echo -e "${BLUE}Select an option:${NC}"
    echo
    echo "1.  Generate 10 lifetime codes"
    echo "2.  Generate 50 lifetime codes"
    echo "3.  Generate 100 lifetime codes"
    echo "4.  Generate 3-day trial codes"
    echo "5.  Generate 7-day trial codes"
    echo "6.  Generate 30-day trial codes"
    echo "7.  Custom generation"
    echo "8.  Import codes from CSV"
    echo "9.  Export codes to CSV"
    echo "10. Validate activation code"
    echo "11. View help"
    echo "12. Exit"
    echo
}

generate_lifetime_codes() {
    local count=$1
    echo
    echo -e "${YELLOW}Generating $count lifetime activation codes...${NC}"
    node "$SCRIPT_DIR/generateActivationCodes.js" --count $count --type lifetime
}

generate_trial_codes() {
    local type=$1
    local default_count=$2
    
    echo
    read -p "Enter number of $type-day trial codes to generate (default $default_count): " trial_count
    trial_count=${trial_count:-$default_count}
    
    echo -e "${YELLOW}Generating $trial_count $type-day trial codes...${NC}"
    node "$SCRIPT_DIR/generateActivationCodes.js" --count $trial_count --type "trial-$type"
}

custom_generation() {
    echo
    echo -e "${PURPLE}Custom Code Generation${NC}"
    echo "====================="
    echo
    
    read -p "Enter number of codes (1-1000): " custom_count
    
    echo
    echo "Code types:"
    echo "1. Lifetime (permanent)"
    echo "2. 3-day trial"
    echo "3. 7-day trial"
    echo "4. 30-day trial"
    echo
    
    read -p "Select code type (1-4): " type_choice
    
    case $type_choice in
        1) custom_type="lifetime" ;;
        2) custom_type="trial-3" ;;
        3) custom_type="trial-7" ;;
        4) custom_type="trial-30" ;;
        *)
            echo -e "${RED}Invalid type selection.${NC}"
            custom_generation
            return
            ;;
    esac
    
    echo
    read -p "Enter batch name (optional): " batch_name
    read -p "Enter output filename (optional): " output_file
    
    echo
    echo -e "${YELLOW}Generating $custom_count $custom_type codes...${NC}"
    
    cmd_args="--count $custom_count --type $custom_type"
    
    if [ ! -z "$batch_name" ]; then
        cmd_args="$cmd_args --batch-name \"$batch_name\""
    fi
    
    if [ ! -z "$output_file" ]; then
        cmd_args="$cmd_args --output \"$output_file\""
    fi
    
    eval "node \"$SCRIPT_DIR/generateActivationCodes.js\" $cmd_args"
}

import_csv() {
    echo
    echo -e "${PURPLE}CSV Import Tool${NC}"
    echo "==============="
    echo
    
    read -p "Enter CSV file path (or drag & drop file here): " csv_file
    
    # Remove quotes if present (from drag & drop)
    csv_file=$(echo "$csv_file" | sed 's/^"\(.*\)"$/\1/')
    
    if [ -z "$csv_file" ]; then
        echo -e "${RED}No file specified.${NC}"
        return
    fi
    
    if [ ! -f "$csv_file" ]; then
        echo -e "${RED}File not found: $csv_file${NC}"
        return
    fi
    
    echo
    echo -e "${YELLOW}Importing codes from $csv_file...${NC}"
    node "$SCRIPT_DIR/importCodesFromCSV.js" "$csv_file"
}

export_csv() {
    echo
    echo -e "${PURPLE}CSV Export Tool${NC}"
    echo "==============="
    echo
    echo "Export options:"
    echo "1. All codes"
    echo "2. Unused codes only"
    echo "3. Activated codes only"
    echo "4. Custom export"
    echo
    
    read -p "Select export option (1-4): " export_choice
    
    case $export_choice in
        1)
            echo -e "${YELLOW}Exporting all codes...${NC}"
            node "$SCRIPT_DIR/exportCodesFromSupabase.js"
            ;;
        2)
            echo -e "${YELLOW}Exporting unused codes...${NC}"
            node "$SCRIPT_DIR/exportCodesFromSupabase.js" --status unused
            ;;
        3)
            echo -e "${YELLOW}Exporting activated codes...${NC}"
            node "$SCRIPT_DIR/exportCodesFromSupabase.js" --status activated
            ;;
        4)
            echo "Custom export options:"
            read -p "Status (unused/activated/blocked/all): " export_status
            read -p "Type (lifetime/trial-3/trial-7/trial-30/all): " export_type
            read -p "Limit (number or leave empty): " export_limit
            read -p "Output file (or leave empty): " export_file
            
            export_args=""
            [ ! -z "$export_status" ] && export_args="$export_args --status $export_status"
            [ ! -z "$export_type" ] && export_args="$export_args --type $export_type"
            [ ! -z "$export_limit" ] && export_args="$export_args --limit $export_limit"
            [ ! -z "$export_file" ] && export_args="$export_args --output \"$export_file\""
            
            echo -e "${YELLOW}Exporting with custom options...${NC}"
            eval "node \"$SCRIPT_DIR/exportCodesFromSupabase.js\" $export_args"
            ;;
        *)
            echo -e "${RED}Invalid choice.${NC}"
            ;;
    esac
}

validate_code() {
    echo
    echo -e "${PURPLE}Code Validation Tool${NC}"
    echo "===================="
    echo
    
    read -p "Enter activation code to validate: " code_to_validate
    
    if [ -z "$code_to_validate" ]; then
        echo -e "${RED}No code specified.${NC}"
        return
    fi
    
    echo
    echo -e "${YELLOW}Validating code: $code_to_validate${NC}"
    node "$SCRIPT_DIR/validateActivationCode.js" "$code_to_validate"
}

show_help() {
    echo
    node "$SCRIPT_DIR/generateActivationCodes.js" --help
}

continue_prompt() {
    echo
    echo "========================================"
    read -p "Do you want to perform another operation? (y/n): " continue_choice
    
    case $continue_choice in
        [Yy]|[Yy][Ee][Ss])
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Main loop
while true; do
    show_menu
    read -p "Enter your choice (1-12): " choice
    
    case $choice in
        1)
            generate_lifetime_codes 10
            ;;
        2)
            generate_lifetime_codes 50
            ;;
        3)
            generate_lifetime_codes 100
            ;;
        4)
            generate_trial_codes "3" 10
            ;;
        5)
            generate_trial_codes "7" 10
            ;;
        6)
            generate_trial_codes "30" 10
            ;;
        7)
            custom_generation
            ;;
        8)
            import_csv
            ;;
        9)
            export_csv
            ;;
        10)
            validate_code
            ;;
        11)
            show_help
            ;;
        12)
            echo
            echo -e "${GREEN}Thank you for using iCalDZ Code Generator!${NC}"
            echo "Press any key to exit..."
            read -n 1
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid choice. Please try again.${NC}"
            continue
            ;;
    esac
    
    if ! continue_prompt; then
        echo
        echo -e "${GREEN}Thank you for using iCalDZ Code Generator!${NC}"
        echo "Press any key to exit..."
        read -n 1
        break
    fi
done
