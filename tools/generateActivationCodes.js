#!/usr/bin/env node

/**
 * iCalDZ Activation Code Generator
 * 
 * This tool generates unique activation codes and inserts them into Supabase.
 * It supports different types of codes and provides various options for code generation.
 * 
 * Usage:
 *   node generateActivationCodes.js [options]
 * 
 * Options:
 *   --count <number>     Number of codes to generate (default: 10)
 *   --type <type>        Type of codes: lifetime, trial-7, trial-30 (default: lifetime)
 *   --batch-name <name>  Name for this batch of codes (optional)
 *   --output <file>      Save codes to file (optional)
 *   --help               Show help
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase configuration
const SUPABASE_URL = 'https://meaorrtisoruuoupldwq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1lYW9ycnRpc29ydXVvdXBsZHdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNzQ3ODgsImV4cCI6MjA2Nzg1MDc4OH0.YERbS4mDoNhaxvF5fHNoQfE4bjvaxqptcN-11cSZjHM';

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Code generation functions
function generateCodePart() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function generateActivationCode(type = 'lifetime') {
  const year = new Date().getFullYear();
  const part1 = generateCodePart();
  const part2 = generateCodePart();
  const part3 = generateCodePart();

  // Different prefixes for different types
  let prefix = 'ICAL';
  switch (type) {
    case 'trial-3':
      prefix = 'TRIAL3';
      break;
    case 'trial-7':
      prefix = 'TRIAL7';
      break;
    case 'trial-30':
      prefix = 'TRIAL30';
      break;
    case 'lifetime':
    default:
      prefix = 'ICAL';
      break;
  }

  return `${prefix}-${year}-${part1}-${part2}-${part3}`;
}

function validateCodeUniqueness(codes) {
  const uniqueCodes = new Set(codes);
  return uniqueCodes.size === codes.length;
}

async function generateCodes(count, type, batchName) {
  console.log(`🔐 Generating ${count} ${type} activation codes...`);
  
  const codes = [];
  const maxAttempts = count * 3; // Prevent infinite loops
  let attempts = 0;
  
  while (codes.length < count && attempts < maxAttempts) {
    const code = generateActivationCode(type);
    
    // Check if code already exists in our array
    if (!codes.includes(code)) {
      codes.push(code);
    }
    
    attempts++;
  }
  
  if (codes.length < count) {
    console.warn(`⚠️ Could only generate ${codes.length} unique codes out of ${count} requested`);
  }
  
  // Validate uniqueness
  if (!validateCodeUniqueness(codes)) {
    throw new Error('Generated codes are not unique!');
  }
  
  console.log(`✅ Generated ${codes.length} unique codes`);
  return codes;
}

async function saveCodesToSupabase(codes, type, batchName) {
  console.log(`💾 Saving ${codes.length} codes to Supabase...`);
  
  const codesData = codes.map(code => ({
    activation_code: code,
    status: 'unused',
    user_name: batchName ? `Batch: ${batchName}` : null,
    created_at: new Date().toISOString()
  }));
  
  try {
    const { data, error } = await supabase
      .from('activations')
      .insert(codesData);
    
    if (error) {
      console.error('❌ Error inserting codes:', error);
      throw error;
    }
    
    console.log(`✅ Successfully saved ${codes.length} codes to Supabase`);
    return true;
  } catch (error) {
    console.error('❌ Failed to save codes to Supabase:', error);
    return false;
  }
}

async function saveCodesToFile(codes, type, batchName, outputFile) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = outputFile || `activation-codes-${type}-${timestamp}.txt`;
  const filepath = path.resolve(filename);
  
  let content = `# iCalDZ Activation Codes\n`;
  content += `# Generated: ${new Date().toLocaleString()}\n`;
  content += `# Type: ${type}\n`;
  content += `# Count: ${codes.length}\n`;
  if (batchName) {
    content += `# Batch: ${batchName}\n`;
  }
  content += `# ==========================================\n\n`;
  
  codes.forEach((code, index) => {
    content += `${index + 1}. ${code}\n`;
  });
  
  content += `\n# ==========================================\n`;
  content += `# Total codes: ${codes.length}\n`;
  content += `# Generated by iCalDZ Code Generator\n`;
  
  try {
    fs.writeFileSync(filepath, content, 'utf8');
    console.log(`📄 Codes saved to file: ${filepath}`);
    return filepath;
  } catch (error) {
    console.error('❌ Failed to save codes to file:', error);
    return null;
  }
}

function showHelp() {
  console.log(`
🔐 iCalDZ Activation Code Generator

Usage:
  node generateActivationCodes.js [options]

Options:
  --count <number>     Number of codes to generate (default: 10)
  --type <type>        Type of codes: lifetime, trial-3, trial-7, trial-30 (default: lifetime)
  --batch-name <name>  Name for this batch of codes (optional)
  --output <file>      Save codes to file (optional)
  --help               Show this help message

Examples:
  node generateActivationCodes.js --count 50
  node generateActivationCodes.js --count 20 --type trial-7
  node generateActivationCodes.js --count 15 --type trial-3
  node generateActivationCodes.js --count 100 --batch-name "January 2025 Batch"
  node generateActivationCodes.js --count 25 --output "my-codes.txt"

Code Types:
  lifetime    - Permanent activation codes (ICAL-YYYY-XXXX-XXXX-XXXX)
  trial-3     - 3-day trial codes (TRIAL3-YYYY-XXXX-XXXX-XXXX)
  trial-7     - 7-day trial codes (TRIAL7-YYYY-XXXX-XXXX-XXXX)
  trial-30    - 30-day trial codes (TRIAL30-YYYY-XXXX-XXXX-XXXX)
`);
}

async function main() {
  console.log('🚀 Starting main function...');
  const args = process.argv.slice(2);
  console.log('📋 Arguments:', args);

  // Parse arguments
  let count = 10;
  let type = 'lifetime';
  let batchName = null;
  let outputFile = null;
  let showHelpFlag = false;
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--count':
        count = parseInt(args[++i]) || 10;
        break;
      case '--type':
        type = args[++i] || 'lifetime';
        break;
      case '--batch-name':
        batchName = args[++i];
        break;
      case '--output':
        outputFile = args[++i];
        break;
      case '--help':
        showHelpFlag = true;
        break;
    }
  }
  
  if (showHelpFlag) {
    showHelp();
    return;
  }
  
  // Validate arguments
  if (count <= 0 || count > 1000) {
    console.error('❌ Count must be between 1 and 1000');
    return;
  }

  if (!['lifetime', 'trial-3', 'trial-7', 'trial-30'].includes(type)) {
    console.error('❌ Type must be one of: lifetime, trial-3, trial-7, trial-30');
    return;
  }
  
  try {
    console.log('🚀 Starting activation code generation...');
    console.log(`📊 Configuration:`);
    console.log(`   Count: ${count}`);
    console.log(`   Type: ${type}`);
    console.log(`   Batch: ${batchName || 'None'}`);
    console.log(`   Output: ${outputFile || 'None'}`);
    console.log('');
    
    // Generate codes
    const codes = await generateCodes(count, type, batchName);
    
    // Save to Supabase
    const supabaseSuccess = await saveCodesToSupabase(codes, type, batchName);
    
    // Save to file if requested
    let fileSuccess = true;
    if (outputFile || !supabaseSuccess) {
      const filepath = await saveCodesToFile(codes, type, batchName, outputFile);
      fileSuccess = !!filepath;
    }
    
    // Summary
    console.log('\n🎉 Code generation completed!');
    console.log(`✅ Generated: ${codes.length} codes`);
    console.log(`${supabaseSuccess ? '✅' : '❌'} Supabase: ${supabaseSuccess ? 'Success' : 'Failed'}`);
    console.log(`${fileSuccess ? '✅' : '❌'} File: ${fileSuccess ? 'Success' : 'Failed'}`);
    
    if (!supabaseSuccess && !fileSuccess) {
      console.error('\n❌ Both Supabase and file operations failed!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error during code generation:', error);
    process.exit(1);
  }
}

// Run the script if this file is executed directly
if (process.argv[1] && import.meta.url.endsWith('generateActivationCodes.js')) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
