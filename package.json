{"name": "icaldz-pos", "private": true, "version": "1.0.0", "type": "module", "description": "iCalDZ Accounting System", "author": "iCode DZ <<EMAIL>>", "main": "main.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "vite build", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "dist": "npm run build && electron-builder --publish=never", "dist:win": "npm run build && electron-builder --win --publish=never", "dist:win-x64": "npm run build && electron-builder --win --x64 --publish=never", "dist:win-ia32": "npm run build && electron-builder --win --ia32 --publish=never", "dist:mac": "npm run build && electron-builder --mac --publish=never", "dist:linux": "npm run build && electron-builder --linux --publish=never", "pack": "npm run build && electron-builder --dir"}, "dependencies": {"@supabase/supabase-js": "^2.50.5", "cors": "^2.8.5", "crypto-js": "^4.2.0", "electron-store": "^10.1.0", "express": "^4.18.2", "jsbarcode": "^3.11.5", "lucide-react": "^0.263.1", "node-machine-id": "^1.1.12", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "concurrently": "^7.6.0", "electron": "^22.0.0", "electron-builder": "^24.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "terser": "^5.40.0", "vite": "^4.4.5", "wait-on": "^7.0.1"}, "build": {"appId": "com.icodedz.accounting", "productName": "iCalDZ Accounting System", "directories": {"output": "../iCalDZ-Installer"}, "files": ["dist/**/*", "main.cjs", "package.json"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}, {"from": "public/", "to": "public/", "filter": ["*.png", "*.svg"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/logo2png.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}