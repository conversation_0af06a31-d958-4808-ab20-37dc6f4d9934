import{g as n}from"./i18n-fa3116d9.js";import{b as e,J as t}from"./vendor-b2b34a58.js";const a=new class{constructor(){this.isInitialized=!1,this.thermalPrinterDetected=!1,this.printerSettings={width:"80mm",fontSize:"14px",fontFamily:"Courier New, monospace",lineHeight:"1.4",margin:"3mm",autocut:!0,encoding:"UTF-8"},this.init()}async init(){try{await this.detectThermalPrinter(),this.isInitialized=!0,console.log("🖨️ Thermal Printer: System initialized successfully")}catch(n){console.warn("🖨️ Thermal Printer: Initialization failed, using fallback mode",n),this.isInitialized=!0}}async detectThermalPrinter(){try{const n=["thermal","receipt","pos","epson","star","citizen","bixolon","custom","zebra","datamax","tsc"];if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){const e=await navigator.mediaDevices.enumerateDevices();this.thermalPrinterDetected=e.some(e=>n.some(n=>e.label.toLowerCase().includes(n)))}return!this.thermalPrinterDetected&&window.print&&(this.thermalPrinterDetected=!0),console.log("🖨️ Thermal Printer: Detection result - "+(this.thermalPrinterDetected?"Found":"Not found")),this.thermalPrinterDetected}catch(n){return console.warn("🖨️ Thermal Printer: Detection failed",n),this.thermalPrinterDetected=!1,!1}}printInvoice(e,t={}){const{language:a="ar",showToast:i=()=>{},formatPrice:r=n=>n.toFixed(2),directPrint:o=!0,storeSettings:s=null}=t;try{const n=this.generateInvoiceContent(e,a,r,s);o&&this.thermalPrinterDetected?this.printDirectly(n,a,i):this.printWithWindow(n,a,i)}catch(l){console.error("🖨️ Thermal Printer: Print failed",l),i(`❌ ${n("printError",a)||"خطأ في الطباعة"}`,"error",3e3)}}generateInvoiceContent(e,t,a,i=null){const r=(e,a)=>n(e,t)||a;return`\n      <!DOCTYPE html>\n      <html dir="${"ar"===t?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">\n        <title>${r("thermalInvoice","فاتورة حرارية")}</title>\n        <style>\n          ${this.getThermalCSS(t)}\n        </style>\n      </head>\n      <body>\n        <div class="thermal-receipt">\n          ${this.generateHeader(t,r,i)}\n          ${this.generateInvoiceInfo(e,t,r)}\n          ${this.generateItemsTable(e,t,r,a)}\n          ${this.generateTotals(e,t,r,a)}\n          ${this.generateFooter(t,r)}\n        </div>\n      </body>\n      </html>\n    `}getThermalCSS(n){const e="ar"===n;return`\n      @page {\n        size: 80mm auto;\n        margin: 0;\n      }\n      \n      * {\n        margin: 0;\n        padding: 0;\n        box-sizing: border-box;\n      }\n      \n      body {\n        font-family: ${this.printerSettings.fontFamily};\n        font-size: ${this.printerSettings.fontSize};\n        font-weight: bold;\n        line-height: ${this.printerSettings.lineHeight};\n        color: #000;\n        background: #fff;\n        width: 74mm;\n        margin: 0 auto;\n        padding: ${this.printerSettings.margin};\n        direction: ${e?"rtl":"ltr"};\n        text-align: center;\n      }\n      \n      .thermal-receipt {\n        width: 100%;\n      }\n      \n      .thermal-header {\n        font-size: 18px;\n        font-weight: bold;\n        text-transform: uppercase;\n        margin-bottom: 4mm;\n        border-bottom: 2px solid #000;\n        padding-bottom: 2mm;\n        text-align: center;\n      }\n\n      .thermal-logo {\n        text-align: center;\n        margin-bottom: 3mm;\n      }\n\n      .thermal-logo img {\n        max-width: 60mm;\n        max-height: 20mm;\n        width: auto;\n        height: auto;\n      }\n\n      .thermal-logo-fallback {\n        font-size: 28px;\n        font-weight: bold;\n        padding: 2mm;\n        display: inline-block;\n      }\n\n      .thermal-store-info {\n        text-align: center;\n        margin: 2mm 0 4mm 0;\n        line-height: 1.6;\n      }\n\n      .thermal-store-name {\n        font-size: 16px;\n        font-weight: bold;\n        margin: 1mm 0;\n      }\n\n      .thermal-store-phone {\n        font-size: 12px;\n        margin: 1mm 0;\n      }\n\n      .thermal-store-address {\n        font-size: 11px;\n        margin: 1mm 0;\n        word-wrap: break-word;\n      }\n\n      .thermal-invoice-title {\n        font-size: 18px;\n        font-weight: bold;\n        text-align: center;\n        margin: 3mm 0 1mm 0;\n        text-transform: uppercase;\n      }\n\n      .thermal-system-info {\n        font-size: 12px;\n        text-align: center;\n        margin-bottom: 3mm;\n      }\n      \n      .thermal-info {\n        font-size: 12px;\n        margin: 2mm 0;\n        text-align: ${e?"right":"left"};\n      }\n      \n      .thermal-table {\n        width: 100%;\n        border-collapse: collapse;\n        margin: 3mm 0;\n        font-size: 10px;\n        table-layout: fixed;\n      }\n\n      .thermal-table th,\n      .thermal-table td {\n        padding: 0.5mm 1mm;\n        border-bottom: 1px solid #000;\n        overflow: hidden;\n        vertical-align: top;\n      }\n\n      .thermal-table th {\n        background: #000;\n        color: #fff;\n        font-weight: bold;\n        font-size: 9px;\n        text-align: center;\n      }\n\n      /* Responsive column widths for thermal printing */\n      .thermal-table .col-product {\n        width: 42%;\n        text-align: ${e?"right":"left"};\n        white-space: normal;\n        word-wrap: break-word;\n        line-height: 1.2;\n      }\n\n      .thermal-table .col-qty {\n        width: 12%;\n        text-align: center;\n        font-weight: bold;\n        font-size: 9px;\n      }\n\n      .thermal-table .col-price {\n        width: 23%;\n        text-align: ${e?"left":"right"};\n        font-size: 9px;\n      }\n\n      .thermal-table .col-total {\n        width: 23%;\n        text-align: ${e?"left":"right"};\n        font-weight: bold;\n        font-size: 10px;\n      }\n\n      /* Responsive adjustments for very small thermal printers */\n      @media print and (max-width: 58mm) {\n        .thermal-table {\n          font-size: 8px;\n        }\n\n        .thermal-table .col-product {\n          width: 45%;\n        }\n\n        .thermal-table .col-qty {\n          width: 10%;\n        }\n\n        .thermal-table .col-price,\n        .thermal-table .col-total {\n          width: 22.5%;\n        }\n      }\n\n      /* Responsive adjustments for larger thermal printers */\n      @media print and (min-width: 80mm) {\n        .thermal-table {\n          font-size: 11px;\n        }\n\n        .thermal-table .col-product {\n          width: 40%;\n        }\n\n        .thermal-table .col-qty {\n          width: 15%;\n        }\n\n        .thermal-table .col-price,\n        .thermal-table .col-total {\n          width: 22.5%;\n        }\n      }\n      \n      .thermal-total {\n        font-size: 16px;\n        font-weight: bold;\n        border: 3px double #000;\n        padding: 3mm;\n        margin: 3mm 0;\n        background: #f0f0f0;\n      }\n      \n      .thermal-footer {\n        font-size: 10px;\n        margin-top: 4mm;\n        border-top: 2px solid #000;\n        padding-top: 3mm;\n        text-align: center;\n      }\n      \n      .center { text-align: center; }\n      .right { text-align: right; }\n      .left { text-align: left; }\n      .bold { font-weight: bold; }\n      .separator { \n        border-top: 1px dashed #000; \n        margin: 2mm 0; \n        height: 1px; \n      }\n      \n      @media print {\n        body { \n          -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n        }\n        .no-print { display: none !important; }\n      }\n    `}generateHeader(n,e,t=null){const a=t||{storeName:"iCalDZ Store",storeNumber:"ST001",storeLogo:"",storePhone:"+*********** 456",storeAddress:"الجزائر العاصمة، الجزائر"};return`\n      <div class="thermal-header">\n        \x3c!-- Logo section --\x3e\n        <div class="thermal-logo">\n          ${a.storeLogo?`<img src="${a.storeLogo}" alt="${a.storeName}" />`:'<div class="thermal-logo-fallback">🏪</div>'}\n        </div>\n\n        \x3c!-- Store information section --\x3e\n        <div class="thermal-store-info">\n          <div class="thermal-store-name">\n            🏪 ${a.storeName} ${a.storeName?"*":""}\n          </div>\n          <div class="thermal-store-phone">\n            📞 ${a.storePhone||"+*********** 456"}\n          </div>\n          <div class="thermal-store-address">\n            📍 ${a.storeAddress||"الجزائر العاصمة، الجزائر"}\n          </div>\n        </div>\n\n        \x3c!-- Invoice title --\x3e\n        <div class="thermal-invoice-title">\n          ${e("salesInvoiceTitle","فاتورة مبيعات")}\n        </div>\n        <div class="thermal-system-info">\n          ${e("accountingSystem","نظام المحاسبي")} - iCalDZ\n        </div>\n      </div>\n    `}generateInvoiceInfo(n,e,t){const a=new Date(n.date).toLocaleDateString("ar"===e?"ar-DZ":"fr"===e?"fr-FR":"en-US"),i=new Date(n.createdAt||n.date).toLocaleTimeString("ar"===e?"ar-DZ":"fr"===e?"fr-FR":"en-US");return`\n      <div class="thermal-info">\n        <div><strong>${t("invoiceNumberLabel","فاتورة رقم:")} ${n.invoiceNumber}</strong></div>\n        <div>${t("dateLabel","التاريخ:")} ${a}</div>\n        <div>${t("timeLabel","الوقت:")} ${i}</div>\n        <div>${t("customerLabel","الزبون:")} ${n.customerName||t("walkInCustomer","زبون عابر")}</div>\n        <div>${t("paymentMethodLabel","طريقة الدفع:")} ${n.paymentMethod}</div>\n      </div>\n      <div class="separator"></div>\n    `}generateItemsTable(n,e,t,a){const i=n.items||[];let r=`\n      <table class="thermal-table">\n        <thead>\n          <tr>\n            <th class="col-product">${t("productColumn","المنتج")}</th>\n            <th class="col-qty">${t("qtyColumn","QNT")}</th>\n            <th class="col-price">${t("priceColumn","السعر")}</th>\n            <th class="col-total">${t("totalColumn","المجموع")}</th>\n          </tr>\n        </thead>\n        <tbody>\n    `;return i.forEach(n=>{let e=n.productName;if(e.length>25){const n=e.split(" ");let t="";for(const e of n){if(!((t+e).length<=22))break;t+=(t?" ":"")+e}e=t+(t.length<e.length?"...":"")}const t=a(n.price).replace(/\s+/g,""),i=a(n.total).replace(/\s+/g,"");r+=`\n        <tr>\n          <td class="col-product">${e}</td>\n          <td class="col-qty">${n.quantity}</td>\n          <td class="col-price">${t}</td>\n          <td class="col-total">${i}</td>\n        </tr>\n      `}),r+='\n        </tbody>\n      </table>\n      <div class="separator"></div>\n    ',r}generateTotals(n,e,t,a){return`\n      <div class="thermal-info">\n        <div>${t("subtotalLabel","المجموع الفرعي:")} ${a(n.subtotal||0)}</div>\n        ${n.discount>0?`<div>${t("discountLabel","الخصم:")} ${a(n.discount)}</div>`:""}\n        ${n.tax>0?`<div>${t("taxLabel","الضريبة:")} ${a(n.tax)}</div>`:""}\n      </div>\n      <div class="thermal-total">\n        ${t("finalTotalLabel","المجموع النهائي:")} ${a(n.finalTotal)}\n      </div>\n    `}generateFooter(n,e){return`\n      <div class="thermal-footer">\n        <div class="bold">${e("thankYouMessage","شكراً لزيارتكم")}</div>\n        <div style="margin-top: 2mm;">\n          ${e("developedBy","تم التطوير بواسطة")} iDesign DZ\n        </div>\n        <div style="margin-top: 2mm; font-size: 8px;">\n          ${e("printedAtLabel","طُبعت في:")} ${(new Date).toLocaleString("ar"===n?"ar-DZ":"fr"===n?"fr-FR":"en-US")}\n        </div>\n      </div>\n    `}printDirectly(e,t,a){try{const i=document.createElement("iframe");i.style.position="absolute",i.style.left="-9999px",i.style.top="-9999px",i.style.width="1px",i.style.height="1px",document.body.appendChild(i);const r=i.contentDocument||i.contentWindow.document;r.open(),r.write(e),r.close(),i.onload=()=>{setTimeout(()=>{try{i.contentWindow.focus(),i.contentWindow.print(),setTimeout(()=>{document.body.removeChild(i)},1e3);a(`🖨️ ${((e,a)=>n(e,t)||a)("invoiceSentToThermalPrinter","تم إرسال الفاتورة للطباعة الحرارية")}`,"success",3e3)}catch(r){console.error("🖨️ Direct print failed, falling back to window method",r),this.printWithWindow(e,t,a)}},500)}}catch(i){console.error("🖨️ Direct printing failed",i),this.printWithWindow(e,t,a)}}printWithWindow(e,t,a){const i=window.open("","_blank","width=400,height=700,scrollbars=yes");if(!i){return void a(`❌ ${((e,a)=>n(e,t)||a)("popupBlocked","تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة")}`,"error",5e3)}i.document.write(e),i.document.close(),i.onload=()=>{setTimeout(()=>{i.focus(),i.print(),setTimeout(()=>{i.close()},2e3)},1e3)};var r,o;a(`🖨️ ${r="printWindowOpened",o="تم فتح نافذة الطباعة",n(r,t)||o}`,"info",3e3)}isThermalPrinterAvailable(){return this.isInitialized&&this.thermalPrinterDetected}getStatus(){return{initialized:this.isInitialized,thermalDetected:this.thermalPrinterDetected,settings:this.printerSettings}}};const i=new class{constructor(){this.canvas=null}async generateRepairQRCode(n){try{const t={id:n.id,clientName:n.clientName,deviceName:n.deviceName,repairPrice:n.repairPrice,status:n.status,date:n.depositDate,type:"repair"},a=JSON.stringify(t);return await e.toDataURL(a,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"},errorCorrectionLevel:"M"})}catch(t){return console.error("Error generating QR code:",t),null}}async generateSmallQRCode(n){try{const t={id:n.id,type:"repair"},a=JSON.stringify(t);return await e.toDataURL(a,{width:80,margin:1,color:{dark:"#000000",light:"#FFFFFF"},errorCorrectionLevel:"L"})}catch(t){return console.error("Error generating small QR code:",t),null}}generateRepairBarcode(n){try{const e=document.createElement("canvas");return t(e,n,{format:"CODE128",width:2,height:50,displayValue:!0,fontSize:12,textMargin:2}),e.toDataURL()}catch(e){return console.error("Error generating barcode:",e),null}}parseQRCode(n){try{const e=JSON.parse(n);return"repair"===e.type&&e.id?e:null}catch(e){return console.error("Error parsing QR code:",e),null}}async generateThermalQRCode(n){try{const t={id:n.id,client:n.clientName,device:n.deviceName,price:n.repairPrice+(n.partsPrice||0),status:n.status,date:n.depositDate,type:"repair_thermal"},a=JSON.stringify(t);return await e.toDataURL(a,{width:120,margin:1,color:{dark:"#000000",light:"#FFFFFF"},errorCorrectionLevel:"H"})}catch(t){return console.error("Error generating thermal QR code:",t),null}}initQRScanner(n,e){return{start:()=>{console.log("QR Scanner started")},stop:()=>{console.log("QR Scanner stopped")},parseManualInput:t=>{const a=this.parseQRCode(t);a?n(a):e("Invalid QR code format")}}}async generatePrintableTicket(n,e={}){try{const t=await this.generateThermalQRCode(n);return{qrCode:t,barcode:this.generateRepairBarcode(n.id),repairData:n,printFormat:e.format||"thermal",language:e.language||"ar",timestamp:(new Date).toISOString()}}catch(t){return console.error("Error generating printable ticket:",t),null}}};const r=new class{constructor(){this.qrTicketWidth="40mm",this.qrTicketHeight="60mm",this.thermalWidth="80mm",this.thermalHeight="80mm",this.isInitialized=!1,this.init()}async init(){try{this.isInitialized=!0,console.log("🖨️ Repair Thermal Printer: System initialized successfully")}catch(n){console.warn("🖨️ Repair Thermal Printer: Initialization failed",n),this.isInitialized=!0}}formatPrice(n,e){const t=parseFloat(n)||0,a=Math.round(t).toString();return"ar"===e?`${a} دج`:`${a} DZD`}formatPriceClean(n){const e=parseFloat(n)||0;return Math.round(e).toString()}formatPhoneNumber(n){if(!n)return"";const e=n.toString(),t=Math.ceil(e.length/2);return`${e.substring(0,t)}<br/>${e.substring(t)}`}async generatePasteTicketContent(n,e={}){try{const{language:t="ar",storeSettings:a={}}=e,r=i.generateRepairBarcode(n.repairBarcode||n.id),o=n.repairPrice||0,s=o-(n.partsPrice||0),l=s>0?s:o,c="ar"===t;return`\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="UTF-8">\n          <title>Paste Ticket - ${n.clientName}</title>\n          <style>\n            @page {\n              size: 45mm 58mm portrait;\n              margin: 1mm;\n            }\n\n            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');\n\n            body {\n              font-family: ${"ar"===t?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', 'Arial', sans-serif"};\n              font-size: ${"ar"===t?"14px":"13px"};\n              font-weight: ${"ar"===t?"700":"900"};\n              line-height: 1.1;\n              margin: 0;\n              padding: ${"ar"===t?"1.5mm":"1mm"};\n              width: 43mm;\n              height: 56mm;\n              direction: ${c?"rtl":"ltr"};\n              text-align: center;\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              justify-content: flex-start;\n            }\n\n            /* Logo and Price in one line at top middle */\n            .header-line {\n              display: flex;\n              justify-content: center;\n              align-items: center;\n              margin-bottom: 2mm;\n              width: 100%;\n              gap: 2mm;\n            }\n\n            .header-line .logo {\n              ${a.storeLogo?"":"font-size: 12px; font-weight: bold;"}\n            }\n\n            .header-line .logo img {\n              width: 12mm;\n              height: 6mm;\n              object-fit: contain;\n              display: block;\n            }\n\n            .header-line .price {\n              font-weight: 900;\n              font-size: ${"ar"===t?"13px":"12px"};\n              border: 2px solid black;\n              padding: 0.5mm 1.5mm;\n              background: #f0f0f0;\n              font-family: ${"ar"===t?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n              direction: ltr;\n              border-radius: 1mm;\n            }\n\n            /* Client Name - Bold and Big */\n            .client-name {\n              font-weight: 900;\n              font-size: ${"ar"===t?"16px":"15px"};\n              margin-bottom: 0.2mm;\n              text-align: center;\n              text-transform: uppercase;\n              font-family: ${"ar"===t?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', 'Arial', sans-serif"};\n              color: #000;\n              line-height: 1.0;\n            }\n\n            /* Phone Number - More space, single line display */\n            .client-phone-number {\n              font-weight: ${"ar"===t?"700":"900"};\n              font-size: ${"ar"===t?"12px":"11px"};\n              margin-bottom: 0.3mm;\n              text-align: center;\n              font-family: ${"ar"===t?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', 'Arial', sans-serif"};\n              direction: ltr;\n              color: #333;\n              padding: 0 1mm;\n            }\n\n            /* Device Name only */\n            .device-name {\n              font-weight: ${"ar"===t?"700":"900"};\n              font-size: ${"ar"===t?"12px":"11px"};\n              margin-bottom: 0.5mm;\n              text-align: center;\n              font-family: ${"ar"===t?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', 'Arial', sans-serif"};\n              color: #333;\n            }\n\n            /* Barcode at bottom */\n            .barcode-section {\n              text-align: center;\n              margin-top: 1mm;\n              width: 100%;\n            }\n\n            .barcode-section img {\n              width: 38mm;\n              height: 7mm;\n              display: block;\n              margin: 0 auto;\n            }\n          </style>\n        </head>\n        <body>\n          \x3c!-- Logo and Price in one line at top middle --\x3e\n          <div class="header-line">\n            <div class="logo">\n              ${a.storeLogo?`<img src="${a.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />`:"🏪"}\n            </div>\n            <div class="price">${this.formatPrice(l,t)}</div>\n          </div>\n\n          \x3c!-- Client Name (Bold and Big) --\x3e\n          <div class="client-name">${n.clientName}</div>\n\n          \x3c!-- Phone Number (Big and Bold, no label) - Single line --\x3e\n          ${n.clientPhone?`<div class="client-phone-number">${n.clientPhone}</div>`:""}\n\n          \x3c!-- Device Name only (no problem description) --\x3e\n          <div class="device-name">${n.deviceName}</div>\n\n          \x3c!-- Barcode at bottom --\x3e\n          <div class="barcode-section">\n            <img src="${r}" alt="Barcode" />\n          </div>\n        </body>\n        </html>\n      `}catch(t){return console.error("Error generating paste ticket content:",t),null}}async generateRepairTicketContent(e,t={}){try{const{language:a="ar",storeSettings:r={}}=t,o=i.generateRepairBarcode(e.repairBarcode||e.id),s=e.repairPrice||0,l=(e.partsPrice,"ar"===a),c=(e,t)=>n(e,a)||t;return`\n        <!DOCTYPE html>\n        <html dir="${l?"rtl":"ltr"}" lang="${a}">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>${c("repairTicket","تذكرة الإصلاح")}</title>\n          <style>\n            ${this.getThermalTicketStyles(a,l)}\n          </style>\n        </head>\n        <body>\n          <div class="thermal-ticket">\n            \x3c!-- Header Section --\x3e\n            <div class="ticket-header">\n              <div class="store-logo">\n                ${r.storeLogo?`<img src="${r.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />`:"🏪"}\n              </div>\n              <div class="store-name">${r.storeName||"ICALDZ STORE"}</div>\n              <div class="store-phone">📞 ${r.storePhone||"+*********** 456"}</div>\n              <div class="store-address">${r.storeAddress||"الجزائر العاصمة، الجزائر"}</div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Ticket Title --\x3e\n            <div class="ticket-title">${c("repairTicket","تذكرة الإصلاح")}</div>\n            <div class="ticket-subtitle">${c("repairSystem","نظام الإصلاح - ICALDZ")}</div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Client Information --\x3e\n            <div class="info-section">\n              <div class="info-line">\n                <span class="label">${c("ticketNumber","رقم التذكرة")}:</span>\n                <span class="value">${e.id||"N/A"}</span>\n              </div>\n              <div class="info-line">\n                <span class="label">${c("date","التاريخ")}:</span>\n                <span class="value">${new Date(e.date||Date.now()).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</span>\n              </div>\n              <div class="info-line">\n                <span class="label">${c("clientName","اسم العميل")}:</span>\n                <span class="value">${e.clientName||"N/A"}</span>\n              </div>\n              <div class="info-line">\n                <span class="label">${c("clientPhone","هاتف العميل")}:</span>\n                <span class="value">${e.clientPhone||e.phone||"N/A"}</span>\n              </div>\n              <div class="info-line">\n                <span class="label">${c("deviceName","اسم الجهاز")}:</span>\n                <span class="value">${e.deviceName||"N/A"}</span>\n              </div>\n              ${e.remarks?`\n              <div class="info-line">\n                <span class="label">${c("remarks","ملاحظات")}:</span>\n                <span class="value">${e.remarks}</span>\n              </div>\n              `:""}\n            </div>\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Barcode Section --\x3e\n            ${o?`\n            <div class="barcode-section">\n              <img src="${o}" alt="Barcode" class="barcode-image" />\n              <div class="barcode-text">${e.repairBarcode||e.id}</div>\n            </div>\n            `:`\n            <div class="barcode-section">\n              <div class="barcode-text-only">${e.repairBarcode||e.id}</div>\n            </div>\n            `}\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Price Section - Only Total Price --\x3e\n            <div class="price-section">\n              <div class="total-price">\n                <span class="label">${c("totalAmount","المبلغ الإجمالي")}:</span>\n                <span class="value">${this.formatPrice(e.repairPrice||0,a)}</span>\n              </div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Footer --\x3e\n            <div class="ticket-footer">\n              <div class="thank-you">${c("thankYou","شكراً لثقتكم")}</div>\n              <div class="developer-info">\n                <div>Développé par iDesign DZ</div>\n              </div>\n              <div class="print-time">${c("printedOn","طُبع في")}: ${(new Date).toLocaleString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `}catch(a){return console.error("Error generating repair ticket content:",a),null}}getThermalTicketStyles(n,e){return`\n      * { margin: 0; padding: 0; box-sizing: border-box; }\n      @page { size: 80mm auto; margin: 0; }\n      @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');\n\n      body {\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        font-size: ${"ar"===n?"14px":"13px"};\n        line-height: 1.3;\n        color: #000;\n        background: white;\n        font-weight: ${"ar"===n?"600":"bold"};\n        direction: ${"ar"===n?"rtl":"ltr"};\n        width: 80mm;\n        margin: 0 auto;\n        padding: ${"ar"===n?"2mm 6mm":"2mm 4mm"};\n        text-align: center;\n        -webkit-print-color-adjust: exact;\n        print-color-adjust: exact;\n        ${"ar"===n?"transform: translateX(2mm);":""}\n      }\n\n      .thermal-ticket {\n        width: 100%;\n        text-align: center;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n\n      .ticket-header {\n        text-align: center;\n        margin-bottom: 3mm;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .store-logo {\n        font-size: 20px;\n        margin-bottom: 2mm;\n        text-align: center;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .store-logo img {\n        width: 25mm;\n        height: 10mm;\n        object-fit: contain;\n        display: block;\n        margin: 0 auto 1mm auto;\n        ${"ar"===n?"margin-left: calc(50% + 2mm);":""}\n      }\n      .store-name {\n        font-size: ${"ar"===n?"16px":"15px"};\n        font-weight: ${"ar"===n?"700":"900"};\n        margin-bottom: 1mm;\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .store-phone {\n        font-size: ${"ar"===n?"13px":"12px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        margin-bottom: 1mm;\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n        direction: ltr;\n      }\n      .store-address {\n        font-size: ${"ar"===n?"12px":"11px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        margin-bottom: 2mm;\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n\n      .ticket-title {\n        font-size: ${"ar"===n?"15px":"14px"};\n        font-weight: ${"ar"===n?"700":"900"};\n        text-align: center;\n        margin: 2mm 0;\n        border: 2px solid #000;\n        padding: 2mm;\n        background: #f0f0f0;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .ticket-subtitle {\n        font-size: ${"ar"===n?"12px":"11px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        text-align: center;\n        margin-bottom: 2mm;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n\n      .separator {\n        border-bottom: 2px solid #000;\n        margin: 2mm 0;\n        width: 100%;\n      }\n      .separator-dotted {\n        border-bottom: 1px dashed #000;\n        margin: 2mm 0;\n        width: 100%;\n      }\n\n      .info-section {\n        margin: 2mm 0;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .info-line {\n        display: flex;\n        justify-content: space-between;\n        margin: 1mm 0;\n        font-size: ${"ar"===n?"13px":"12px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .info-line .label {\n        font-weight: ${"ar"===n?"700":"900"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n      }\n      .info-line .value {\n        text-align: ${e?"left":"right"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n      }\n\n      .barcode-section {\n        text-align: center;\n        margin: 3mm 0;\n        padding: 2mm;\n        border: 2px solid #000;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .barcode-image {\n        max-width: 100%;\n        height: auto;\n        margin: 0 auto 3mm auto;\n        display: block;\n      }\n      .barcode-text {\n        font-family: 'Courier New', monospace;\n        font-size: 12px;\n        font-weight: 900;\n        text-align: center;\n      }\n      .barcode-text-only {\n        font-family: 'Courier New', monospace;\n        font-size: 14px;\n        font-weight: 900;\n        padding: 4mm;\n        text-align: center;\n      }\n\n      .price-section {\n        margin: 2mm 0;\n      }\n      .price-line {\n        display: flex;\n        justify-content: space-between;\n        margin: 1mm 0;\n        font-size: ${"ar"===n?"13px":"12px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n      }\n      .price-line .label {\n        font-weight: ${"ar"===n?"700":"900"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n      }\n      .price-line .value {\n        font-weight: ${"ar"===n?"700":"900"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n      }\n      .total-price {\n        display: flex;\n        justify-content: space-between;\n        margin: 2mm 0;\n        font-size: ${"ar"===n?"15px":"14px"};\n        font-weight: ${"ar"===n?"700":"900"};\n        border: 2px solid #000;\n        padding: 2mm;\n        background: #f0f0f0;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n      }\n\n      /* Footer Styles - Bigger and Clearer */\n      .ticket-footer {\n        text-align: center;\n        margin-top: 4mm;\n        font-size: 12px;\n        font-weight: ${"ar"===n?"600":"bold"};\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n        width: 100%;\n      }\n      .thank-you {\n        font-weight: ${"ar"===n?"700":"900"};\n        margin-bottom: 3mm;\n        font-size: 14px;\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n      }\n      .developer-info {\n        border-top: 2px solid #000;\n        padding-top: 3mm;\n        margin: 3mm 0;\n        font-weight: ${"ar"===n?"600":"bold"};\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n      }\n      .print-time {\n        font-size: 10px;\n        margin-top: 3mm;\n        font-weight: ${"ar"===n?"600":"bold"};\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', 'Arial'"}, sans-serif;\n      }\n\n      @media print {\n        body {\n          -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n          text-align: center;\n        }\n        .thermal-ticket {\n          page-break-inside: avoid;\n          text-align: center;\n          margin: 0 auto;\n        }\n        * {\n          text-align: center !important;\n        }\n      }\n    `}async openPrintWindow(n,e={}){try{const t=await this.generateRepairTicketContent(n,e);if(!t)throw new Error("Failed to generate ticket content");const a=window.open("","_blank","width=400,height=700");return a.document.write(t),a.document.close(),a.onload=()=>{setTimeout(()=>{a.print(),a.close()},500)},!0}catch(t){return console.error("Error opening print window:",t),!1}}async openPasteTicketPrintWindow(n,e={}){try{const t=await this.generatePasteTicketContent(n,e);if(!t)throw new Error("Failed to generate paste ticket content");const a=window.open("","_blank","width=300,height=200");return a.document.write(t),a.document.close(),a.onload=()=>{setTimeout(()=>{a.print(),a.close()},500)},!0}catch(t){return console.error("Error opening paste ticket print window:",t),!1}}async printRepairOrderReceipt(n,e={}){return this.openPrintWindow(n,e)}async printClientPickupReceipt(n,e={}){try{const t=await this.generateClientInvoiceContent(n,e);if(!t)throw new Error("Failed to generate client invoice content");const a=window.open("","_blank","width=400,height=700");return a.document.write(t),a.document.close(),a.onload=()=>{setTimeout(()=>{a.print(),a.close()},500)},!0}catch(t){return console.error("Error printing client pickup receipt:",t),!1}}async printRepairInvoice(n,e={}){try{const t=await this.generateRepairInvoiceContent(n,e);if(!t)throw new Error("Failed to generate repair invoice content");const a=window.open("","_blank","width=400,height=700");return a.document.write(t),a.document.close(),a.onload=()=>{setTimeout(()=>{a.print(),a.close()},500)},!0}catch(t){return console.error("Error printing repair invoice:",t),!1}}async generateRepairInvoiceContent(e,t={}){try{const{language:a="ar",storeSettings:i={}}=t,r="ar"===a,o=(e,t)=>n(e,a)||t,s=parseFloat(e.repairPrice||0),l=parseFloat(e.partsPrice||0),c=s-l,d=s,m=parseFloat(i.taxRate||0),p=d*m/100,g=d+p,h=`REP-INV-${Date.now()}`;return`\n        <!DOCTYPE html>\n        <html dir="${r?"rtl":"ltr"}" lang="${a}">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>${o("repairInvoice","فاتورة الإصلاح")}</title>\n          <style>\n            ${this.getThermalInvoiceStyles(a,r)}\n          </style>\n        </head>\n        <body>\n          <div class="thermal-invoice">\n            \x3c!-- Header Section --\x3e\n            <div class="invoice-header">\n              <div class="store-logo">\n                ${i.storeLogo?`<img src="${i.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />`:"🏪"}\n              </div>\n              <div class="store-name">${i.storeName||"ICALDZ STORE"} ⭐</div>\n              <div class="store-phone">📞 ${i.storePhone||"+*********** 456"}</div>\n              <div class="store-address">${i.storeAddress||"الجزائر العاصمة، الجزائر"}</div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Invoice Title --\x3e\n            <div class="invoice-title">${o("repairInvoice","فاتورة الإصلاح")}</div>\n            <div class="invoice-subtitle">${o("repairSystem","نظام الإصلاح - ICALDZ")}</div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Invoice Details --\x3e\n            <div class="invoice-details">\n              <div class="detail-line">\n                <span class="label">${o("invoiceNumber","رقم الفاتورة")}:</span>\n                <span class="value">${h}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("repairId","رقم الإصلاح")}:</span>\n                <span class="value">${e.id||e.repairBarcode||"N/A"}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("date","التاريخ")}:</span>\n                <span class="value">${(new Date).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("clientName","اسم العميل")}:</span>\n                <span class="value">${e.clientName||"Client de passage"}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("deviceName","اسم الجهاز")}:</span>\n                <span class="value">${e.deviceName||"N/A"}</span>\n              </div>\n            </div>\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Detailed Pricing Section --\x3e\n            <div class="pricing-details">\n              <div class="pricing-title">${o("pricingDetails","تفاصيل التسعير")}</div>\n\n              <div class="price-line">\n                <span class="label">${o("repairPrice","سعر الإصلاح")}:</span>\n                <span class="value">${this.formatPrice(s,a)}</span>\n              </div>\n\n              ${l&&parseFloat(l)>0?`\n              <div class="price-line">\n                <span class="label">${o("partsPrice","سعر القطع")}:</span>\n                <span class="value">${this.formatPrice(l,a)}</span>\n              </div>\n              <div class="price-line">\n                <span class="label">${o("interestRate","معدل الفائدة")}:</span>\n                <span class="value">${this.formatPrice(c,a)}</span>\n              </div>\n              `:""}\n\n              <div class="separator-dotted"></div>\n\n              <div class="total-line">\n                <span class="label">${o("subtotal","المجموع الفرعي")}:</span>\n                <span class="value">${this.formatPrice(d,a)}</span>\n              </div>\n              <div class="total-line">\n                <span class="label">${o("tax","الضريبة")} ${m}%:</span>\n                <span class="value">${this.formatPrice(p,a)}</span>\n              </div>\n              <div class="final-total">\n                <span class="label">${o("finalTotal","المجموع النهائي")}:</span>\n                <span class="value">${this.formatPrice(g,a)}</span>\n              </div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Footer --\x3e\n            <div class="invoice-footer">\n              <div class="thank-you">${o("thankYouVisit","شكراً لزيارتكم")}</div>\n              <div class="developer-info">\n                <div>Développé par iDesign DZ</div>\n              </div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `}catch(a){return console.error("Error generating repair invoice content:",a),null}}async generateClientInvoiceContent(e,t={}){try{const{language:a="ar",storeSettings:i={}}=t,r="ar"===a,o=(e,t)=>n(e,a)||t,s=parseFloat(e.repairPrice||0),l=(parseFloat(e.partsPrice||0),s),c=parseFloat(i.taxRate||0),d=l*c/100,m=l+d,p=`INV-${Date.now()}`;return`\n        <!DOCTYPE html>\n        <html dir="${r?"rtl":"ltr"}" lang="${a}">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>${o("clientInvoice","فاتورة العميل")}</title>\n          <style>\n            ${this.getThermalInvoiceStyles(a,r)}\n          </style>\n        </head>\n        <body>\n          <div class="thermal-invoice">\n            \x3c!-- Header Section --\x3e\n            <div class="invoice-header">\n              <div class="store-logo">\n                ${i.storeLogo?`<img src="${i.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />`:"🏪"}\n              </div>\n              <div class="store-name">${i.storeName||"ICALDZ STORE"} ⭐</div>\n              <div class="store-phone">📞 ${i.storePhone||"+*********** 456"}</div>\n              <div class="store-address">${i.storeAddress||"الجزائر العاصمة، الجزائر"}</div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Invoice Title --\x3e\n            <div class="invoice-title">${o("clientInvoice","فاتورة العميل")}</div>\n            <div class="invoice-subtitle">${o("repairSystem","نظام الإصلاح - ICALDZ")}</div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Invoice Details --\x3e\n            <div class="invoice-details">\n              <div class="detail-line">\n                <span class="label">${o("invoiceNumber","رقم الفاتورة")}:</span>\n                <span class="value">${p}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("date","التاريخ")}:</span>\n                <span class="value">${(new Date).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("time","الوقت")}:</span>\n                <span class="value">${(new Date).toLocaleTimeString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("clientName","اسم العميل")}:</span>\n                <span class="value">${e.clientName||"Client de passage"}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${o("paymentMode","طريقة الدفع")}:</span>\n                <span class="value">${o("cash","نقداً")}</span>\n              </div>\n            </div>\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Products Table --\x3e\n            <div class="products-table">\n              <div class="table-header">\n                <span class="col-product">${o("service","الخدمة")}</span>\n                <span class="col-total">${"ar"===a?o("total","المجموع"):"Prix Total"}</span>\n              </div>\n              <div class="table-row">\n                <span class="col-product">${e.deviceName||"Réparation"}</span>\n                <span class="col-total">${this.formatPriceClean(s)}</span>\n              </div>\n            </div>\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Totals Section --\x3e\n            <div class="totals-section">\n              <div class="total-line">\n                <span class="label">${o("subtotal","المجموع الفرعي")}:</span>\n                <span class="value">${this.formatPrice(l,a)}</span>\n              </div>\n              <div class="total-line">\n                <span class="label">${o("tax","الضريبة")} ${c}%:</span>\n                <span class="value">${this.formatPrice(d,a)}</span>\n              </div>\n              <div class="final-total">\n                <span class="label">${o("finalTotal","المجموع النهائي")}:</span>\n                <span class="value">${this.formatPrice(m,a)}</span>\n              </div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Footer --\x3e\n            <div class="invoice-footer">\n              <div class="thank-you">${o("thankYouVisit","شكراً لزيارتكم")}</div>\n              <div class="developer-info">\n                <div>Développé par iDesign DZ</div>\n              </div>\n              <div class="print-time">${o("printedOn","طُبع في")}: ${(new Date).toLocaleString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `}catch(a){return console.error("Error generating client invoice content:",a),null}}getThermalInvoiceStyles(n,e){return`\n      * { margin: 0; padding: 0; box-sizing: border-box; }\n      @page { size: 80mm auto; margin: 0; }\n      @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');\n      body {\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        font-size: ${"ar"===n?"14px":"13px"};\n        line-height: 1.3;\n        color: #000;\n        background: white;\n        font-weight: ${"ar"===n?"600":"bold"};\n        direction: ${"ar"===n?"rtl":"ltr"};\n        width: 80mm;\n        margin: 0 auto;\n        padding: ${"ar"===n?"2mm 6mm":"2mm 4mm"};\n        text-align: center;\n        -webkit-print-color-adjust: exact;\n        print-color-adjust: exact;\n        ${"ar"===n?"transform: translateX(2mm);":""}\n      }\n      .thermal-invoice {\n        width: 100%;\n        text-align: center;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif":"'Arial Black', sans-serif"};\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n\n      /* Header Styles - Bigger and Clearer */\n      .invoice-header {\n        text-align: center;\n        margin-bottom: 4mm;\n        width: 100%;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .store-logo {\n        font-size: 24px;\n        margin-bottom: 3mm;\n        text-align: center;\n        width: 100%;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .store-logo img {\n        width: 30mm;\n        height: 12mm;\n        object-fit: contain;\n        margin: 0 auto 2mm auto;\n        display: block;\n        ${"ar"===n?"margin-left: calc(50% + 2mm);":""}\n      }\n      .store-name {\n        font-size: ${"ar"===n?"20px":"18px"};\n        font-weight: ${"ar"===n?"700":"900"};\n        margin-bottom: 2mm;\n        text-align: center;\n        width: 100%;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .store-phone {\n        font-size: ${"ar"===n?"16px":"14px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        margin-bottom: 2mm;\n        text-align: center;\n        width: 100%;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: 2mm;":""}\n        direction: ltr;\n      }\n      .store-address {\n        font-size: ${"ar"===n?"14px":"12px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        margin-bottom: 3mm;\n        text-align: center;\n        word-wrap: break-word;\n        width: 100%;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n\n      /* Title Styles - Bigger and Clearer */\n      .invoice-title {\n        font-size: ${"ar"===n?"18px":"16px"};\n        font-weight: ${"ar"===n?"700":"900"};\n        text-align: center;\n        margin: 3mm auto;\n        border: 2px solid #000;\n        padding: 4mm;\n        background: #f0f0f0;\n        width: 95%;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: calc(2.5% + 2mm);":""}\n      }\n      .invoice-subtitle {\n        font-size: ${"ar"===n?"14px":"12px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        text-align: center;\n        margin-bottom: 3mm;\n        font-family: ${"ar"===n?"'Cairo', 'Tahoma', 'Arial Unicode MS'":"'Arial Black', 'Arial'"}, sans-serif;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n\n      /* Separator Styles - Thicker */\n      .separator { border-bottom: 3px solid #000; margin: 3mm 0; }\n      .separator-dotted { border-bottom: 2px dashed #000; margin: 3mm 0; }\n\n      /* Invoice Details - Bigger and Clearer */\n      .invoice-details {\n        margin: 3mm 0;\n        width: 100%;\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .detail-line {\n        display: flex;\n        justify-content: space-between;\n        margin: 2mm 0;\n        font-size: ${"ar"===n?"14px":"13px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        width: 100%;\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .detail-line .label {\n        font-weight: ${"ar"===n?"700":"900"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n      }\n      .detail-line .value {\n        text-align: ${e?"left":"right"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n      }\n\n      /* Products Table - Simplified 3-Column Layout */\n      .products-table {\n        margin: 3mm 0;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .table-header {\n        display: flex;\n        background: #000;\n        color: white;\n        padding: 2mm;\n        font-size: 14px;\n        font-weight: 900;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .table-row {\n        display: flex;\n        padding: 2mm;\n        font-size: 14px;\n        font-weight: bold;\n        border-bottom: 2px dotted #000;\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .col-product { flex: 2.5; font-weight: bold; text-align: ${e?"right":"left"}; }\n      .col-total { flex: 2; text-align: ${e?"right":"left"}; font-weight: bold; }\n\n      /* Totals Section - Bigger and Clearer */\n      .totals-section {\n        margin: 3mm 0;\n        width: 100%;\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .total-line {\n        display: flex;\n        justify-content: space-between;\n        margin: 2mm 0;\n        font-size: ${"ar"===n?"15px":"13px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        width: 100%;\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .total-line .label {\n        font-weight: ${"ar"===n?"700":"900"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n      }\n      .total-line .value {\n        font-weight: ${"ar"===n?"700":"900"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n      }\n      .final-total {\n        display: flex;\n        justify-content: space-between;\n        margin: 3mm auto;\n        font-size: ${"ar"===n?"20px":"18px"};\n        font-weight: ${"ar"===n?"700":"900"};\n        border: 3px solid #000;\n        padding: 5mm;\n        background: #f0f0f0;\n        width: 95%;\n        text-align: center;\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: calc(2.5% + 2mm);":""}\n      }\n\n      /* Footer Styles - Bigger and Clearer */\n      .invoice-footer {\n        text-align: center;\n        margin-top: 4mm;\n        font-size: ${"ar"===n?"14px":"12px"};\n        font-weight: ${"ar"===n?"600":"bold"};\n        width: 100%;\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .thank-you {\n        font-weight: ${"ar"===n?"700":"900"};\n        margin-bottom: 3mm;\n        font-size: ${"ar"===n?"16px":"14px"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .developer-info {\n        border-top: 2px solid #000;\n        padding-top: 3mm;\n        margin: 3mm 0;\n        font-weight: ${"ar"===n?"600":"bold"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n        ${"ar"===n?"margin-left: 2mm;":""}\n      }\n      .print-time {\n        font-size: ${"ar"===n?"12px":"10px"};\n        margin-top: 3mm;\n        font-weight: ${"ar"===n?"600":"bold"};\n        ${"ar"===n?'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;':""}\n      }\n\n      @media print {\n        body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }\n        .thermal-invoice { page-break-inside: avoid; }\n      }\n    `}async generateSupplierTransactionContent(e,t,a,i={},r=[]){try{const{language:o="ar",storeSettings:s={}}=i,l="ar"===o,c=(e,t)=>n(e,o)||t;return`\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="UTF-8">\n          <title>Supplier Transactions - ${e}</title>\n          <style>\n            ${this.getThermalInvoiceStyles(o,l)}\n          </style>\n        </head>\n        <body>\n          <div class="thermal-invoice">\n            \x3c!-- Header Section --\x3e\n            <div class="invoice-header">\n              <div class="store-logo">\n                ${s.storeLogo?`<img src="${s.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />`:"🏪"}\n              </div>\n              <div class="store-name">${s.storeName||"ICALDZ STORE"} ⭐</div>\n              <div class="store-phone">📞 ${s.storePhone||"+*********** 456"}</div>\n              <div class="store-address">${s.storeAddress||"الجزائر العاصمة، الجزائر"}</div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Invoice Title --\x3e\n            <div class="invoice-title">${c("supplierTransactions","معاملات المورد")}</div>\n            <div class="invoice-subtitle">${e}</div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Invoice Details --\x3e\n            <div class="invoice-details">\n              <div class="detail-line">\n                <span class="label">${c("supplierName","اسم المورد")}:</span>\n                <span class="value">${e}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${c("printedOn","طُبع في")}:</span>\n                <span class="value">${(new Date).toLocaleDateString("ar"===o?"ar-DZ":"fr"===o?"fr-FR":"en-US")}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${c("time","الوقت")}:</span>\n                <span class="value">${(new Date).toLocaleTimeString("ar"===o?"ar-DZ":"fr"===o?"fr-FR":"en-US")}</span>\n              </div>\n              <div class="detail-line">\n                <span class="label">${c("totalTransactions","إجمالي المعاملات")}:</span>\n                <span class="value">${t.length}</span>\n              </div>\n            </div>\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Transactions Table --\x3e\n            <div class="products-table">\n              <div class="table-header">\n                <span class="col-product">${c("deviceProblem","الجهاز والمشكلة")}</span>\n                <span class="col-price">${c("partsPrice","سعر القطع")}</span>\n                <span class="col-total">${c("status","الحالة")}</span>\n              </div>\n              ${t.map(n=>{let e="";if(n.repairId&&r.length>0){const t=r.find(e=>e.id===n.repairId);if(t){const n=t.deviceName||"",a=t.problemDescription||t.problemType||"";e=a?`${n} - ${a}`:n}}if(!e&&n.partName){const t=n.partName.split(" - ");if(t.length>1){e=t[0].replace("Parts for ","").replace("قطع لـ ","")}else e=n.partName}else!e&&n.deviceName&&n.problem?e=`${n.deviceName} - ${n.problem}`:!e&&n.deviceName?e=n.deviceName:e||(e=c("repairParts","قطع الإصلاح"));return`\n              <div class="table-row">\n                <span class="col-product">${e}</span>\n                <span class="col-price">${this.formatPrice(n.price,o)}</span>\n                <span class="col-total">${n.paid?c("paid","مدفوع"):c("pending","معلق")}</span>\n              </div>\n              `}).join("")}\n            </div>\n\n            <div class="separator-dotted"></div>\n\n            \x3c!-- Totals Section --\x3e\n            <div class="totals-section">\n              <div class="total-line">\n                <span class="label">${c("totalAmount","المبلغ الإجمالي")}:</span>\n                <span class="value">${this.formatPrice(t.reduce((n,e)=>n+e.price,0),o)}</span>\n              </div>\n              <div class="total-line">\n                <span class="label">${c("paidAmount","المبلغ المدفوع")}:</span>\n                <span class="value">${this.formatPrice(t.filter(n=>n.paid).reduce((n,e)=>n+e.price,0),o)}</span>\n              </div>\n              <div class="final-total">\n                <span class="label">${c("remainingCredit","الائتمان المتبقي")}:</span>\n                <span class="value">${this.formatPrice(a,o)}</span>\n              </div>\n            </div>\n\n            <div class="separator"></div>\n\n            \x3c!-- Footer --\x3e\n            <div class="invoice-footer">\n              <div class="thank-you">${c("thankYouBusiness","شكراً لتعاملكم معنا")}</div>\n              <div class="developer-info">\n                <div>🔧 ${c("repairSystem","نظام الإصلاح - ICALDZ")}</div>\n              </div>\n              <div class="print-time">${c("printedOn","طُبع في")}: ${(new Date).toLocaleString("ar"===o?"ar-DZ":"fr"===o?"fr-FR":"en-US")}</div>\n            </div>\n          </div>\n        </body>\n        </html>\n      `}catch(o){return console.error("Error generating supplier transaction content:",o),null}}async printSupplierTransactions(n,e,t,a={},i=[]){console.log("🖨️ RepairThermalPrinter.printSupplierTransactions called"),console.log("📊 Supplier:",n,"Transactions:",e.length,"Credit:",t);try{const r=await this.generateSupplierTransactionContent(n,e,t,a,i);if(!r)throw console.error("❌ Failed to generate supplier transaction content"),new Error("Failed to generate supplier transaction content");console.log("✅ Content generated successfully");const o=window.open("","_blank","width=400,height=700");return o.document.write(r),o.document.close(),o.onload=()=>{setTimeout(()=>{o.print(),o.close()},500)},!0}catch(r){return console.error("Error printing supplier transactions:",r),!1}}async printRepairOrdersList(n,e={}){return console.log("Printing repair orders list:",n.length),!0}generatePasteTicketProductContent(e,t,a){try{const i="ar"===t,r=(e,a)=>n(e,t)||a,o=e.barcode||e.id;return`\n        <!DOCTYPE html>\n        <html dir="${i?"rtl":"ltr"}" lang="${t}">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>${r("pasteTicketProduct","ملصق المنتج")}</title>\n          ${this.getPasteTicketProductStyles(t,i)}\n        </head>\n        <body>\n          <div class="paste-ticket-container">\n            \x3c!-- Barcode Section --\x3e\n            <div class="barcode-section">\n              <div class="barcode-container">\n                <svg id="barcode"></svg>\n              </div>\n            </div>\n\n            \x3c!-- Logo Section --\x3e\n            <div class="logo-section">\n              ${a.storeLogo?`<img src="${a.storeLogo}" alt="Logo" class="store-logo" />`:'<div class="logo-placeholder">🏪</div>'}\n            </div>\n\n            \x3c!-- Product Name Section --\x3e\n            <div class="product-name-section">\n              <div class="product-name">${e.name||e.productName||""}</div>\n            </div>\n\n            \x3c!-- Category Section --\x3e\n            <div class="category-section">\n              <div class="category-label">${e.category||r("noCategory","بدون فئة")}</div>\n            </div>\n          </div>\n\n          <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"><\/script>\n          <script>\n            // Generate barcode\n            try {\n              JsBarcode("#barcode", "${o}", {\n                format: "CODE128",\n                width: 1.5,\n                height: 25,\n                displayValue: false,\n                margin: 0,\n                background: "#ffffff",\n                lineColor: "#000000"\n              });\n            } catch (error) {\n              console.error('Barcode generation failed:', error);\n              document.getElementById('barcode').innerHTML = '<text x="50%" y="50%" text-anchor="middle">${o}</text>';\n            }\n\n            // Auto print when ready\n            window.onload = function() {\n              setTimeout(() => {\n                window.print();\n              }, 500);\n            };\n          <\/script>\n        </body>\n        </html>\n      `}catch(i){return console.error("Error generating paste ticket product content:",i),null}}getPasteTicketProductStyles(n,e){return`\n      <style>\n        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');\n\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n\n        @page {\n          size: 45mm 35mm;\n          margin: 1mm;\n        }\n\n        body {\n          font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', Arial"}, sans-serif;\n          font-size: 8px;\n          line-height: 1.1;\n          color: #000;\n          background: #fff;\n          direction: ${e?"rtl":"ltr"};\n          text-align: center;\n        }\n\n        .paste-ticket-container {\n          width: 43mm;\n          height: 33mm;\n          display: flex;\n          flex-direction: column;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1mm;\n          border: 1px solid #000;\n        }\n\n        .barcode-section {\n          width: 100%;\n          height: 8mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 1mm;\n        }\n\n        .barcode-container {\n          width: 35mm;\n          height: 6mm;\n        }\n\n        .barcode-container svg {\n          width: 100%;\n          height: 100%;\n        }\n\n        .logo-section {\n          width: 100%;\n          height: 6mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 1mm;\n        }\n\n        .store-logo {\n          max-width: 15mm;\n          max-height: 5mm;\n          object-fit: contain;\n        }\n\n        .logo-placeholder {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .product-name-section {\n          width: 100%;\n          height: 8mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 1mm;\n        }\n\n        .product-name {\n          font-size: 9px;\n          font-weight: 900;\n          color: #000;\n          text-align: center;\n          line-height: 1.1;\n          word-wrap: break-word;\n          overflow: hidden;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n        }\n\n        .category-section {\n          width: 100%;\n          height: 6mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n        }\n\n        .category-label {\n          font-size: 8px;\n          font-weight: 700;\n          color: #333;\n          text-align: center;\n          text-transform: uppercase;\n          background: #f0f0f0;\n          padding: 1mm 2mm;\n          border-radius: 1mm;\n          border: 1px solid #ccc;\n        }\n\n        @media print {\n          body {\n            -webkit-print-color-adjust: exact;\n            print-color-adjust: exact;\n          }\n          .no-print { display: none !important; }\n        }\n      </style>\n    `}async printPasteTicketProduct(e,t={}){const{language:a="ar",showToast:i=()=>{},storeSettings:r={}}=t;try{const t=this.generatePasteTicketProductContent(e,a,r);if(!t)throw new Error("Failed to generate paste ticket content");const o=window.open("","_blank","width=400,height=300");return o.document.write(t),o.document.close(),o.focus(),i(`🖨️ ${n("printingPasteTicketProduct",a)||"طباعة ملصق المنتج"}`,"info",2e3),!0}catch(o){return console.error("🖨️ Paste Ticket Product Print Error:",o),i(`❌ ${n("printError",a)||"خطأ في الطباعة"}`,"error",3e3),!1}}generatePasteTicketPriceContent(e,t,a){try{const i="ar"===t,r=(e,a)=>n(e,t)||a,o=e.barcode||e.id,s=e.sellPrice||e.price||0;return`\n        <!DOCTYPE html>\n        <html dir="${i?"rtl":"ltr"}" lang="${t}">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>${r("pasteTicketPrice","ملصق السعر")}</title>\n          ${this.getPasteTicketPriceStyles(t,i)}\n        </head>\n        <body>\n          <div class="paste-ticket-container">\n            \x3c!-- Barcode Section --\x3e\n            <div class="barcode-section">\n              <div class="barcode-container">\n                <svg id="barcode"></svg>\n              </div>\n            </div>\n\n            \x3c!-- Logo Section --\x3e\n            <div class="logo-section">\n              ${a.storeLogo?`<img src="${a.storeLogo}" alt="Logo" class="store-logo" />`:'<div class="logo-placeholder">🏪</div>'}\n            </div>\n\n            \x3c!-- Product Name Section --\x3e\n            <div class="product-name-section">\n              <div class="product-name">${e.name||e.productName||""}</div>\n            </div>\n\n            \x3c!-- Category and Price Section --\x3e\n            <div class="category-price-section">\n              <div class="category-label">${e.category||r("noCategory","بدون فئة")}</div>\n              <div class="price-label">${this.formatPrice(s,t)}</div>\n            </div>\n          </div>\n\n          <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"><\/script>\n          <script>\n            // Generate barcode\n            try {\n              JsBarcode("#barcode", "${o}", {\n                format: "CODE128",\n                width: 1.5,\n                height: 25,\n                displayValue: false,\n                margin: 0,\n                background: "#ffffff",\n                lineColor: "#000000"\n              });\n            } catch (error) {\n              console.error('Barcode generation failed:', error);\n              document.getElementById('barcode').innerHTML = '<text x="50%" y="50%" text-anchor="middle">${o}</text>';\n            }\n\n            // Auto print when ready\n            window.onload = function() {\n              setTimeout(() => {\n                window.print();\n              }, 500);\n            };\n          <\/script>\n        </body>\n        </html>\n      `}catch(i){return console.error("Error generating paste ticket price content:",i),null}}getPasteTicketPriceStyles(n,e){return`\n      <style>\n        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');\n\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n\n        @page {\n          size: 45mm 35mm;\n          margin: 1mm;\n        }\n\n        body {\n          font-family: ${"ar"===n?"'Cairo', 'Tahoma'":"'Arial Black', Arial"}, sans-serif;\n          font-size: 8px;\n          line-height: 1.1;\n          color: #000;\n          background: #fff;\n          direction: ${e?"rtl":"ltr"};\n          text-align: center;\n        }\n\n        .paste-ticket-container {\n          width: 43mm;\n          height: 33mm;\n          display: flex;\n          flex-direction: column;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1mm;\n          border: 1px solid #000;\n        }\n\n        .barcode-section {\n          width: 100%;\n          height: 8mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 1mm;\n        }\n\n        .barcode-container {\n          width: 35mm;\n          height: 6mm;\n        }\n\n        .barcode-container svg {\n          width: 100%;\n          height: 100%;\n        }\n\n        .logo-section {\n          width: 100%;\n          height: 6mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 1mm;\n        }\n\n        .store-logo {\n          max-width: 15mm;\n          max-height: 5mm;\n          object-fit: contain;\n        }\n\n        .logo-placeholder {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .product-name-section {\n          width: 100%;\n          height: 6mm;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 1mm;\n        }\n\n        .product-name {\n          font-size: 8px;\n          font-weight: 700;\n          color: #000;\n          text-align: center;\n          line-height: 1.1;\n          word-wrap: break-word;\n          overflow: hidden;\n          display: -webkit-box;\n          -webkit-line-clamp: 1;\n          -webkit-box-orient: vertical;\n        }\n\n        .category-price-section {\n          width: 100%;\n          height: 8mm;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          align-items: center;\n          gap: 1mm;\n        }\n\n        .category-label {\n          font-size: 7px;\n          font-weight: 700;\n          color: #333;\n          text-align: center;\n          text-transform: uppercase;\n          background: #f0f0f0;\n          padding: 0.5mm 1.5mm;\n          border-radius: 1mm;\n          border: 1px solid #ccc;\n        }\n\n        .price-label {\n          font-size: 10px;\n          font-weight: 900;\n          color: #000;\n          text-align: center;\n          background: #ffeb3b;\n          padding: 1mm 2mm;\n          border-radius: 1mm;\n          border: 2px solid #000;\n          box-shadow: 0 1mm 2mm rgba(0,0,0,0.3);\n        }\n\n        @media print {\n          body {\n            -webkit-print-color-adjust: exact;\n            print-color-adjust: exact;\n          }\n          .no-print { display: none !important; }\n        }\n      </style>\n    `}async printPasteTicketPrice(e,t={}){const{language:a="ar",showToast:i=()=>{},storeSettings:r={}}=t;try{const t=this.generatePasteTicketPriceContent(e,a,r);if(!t)throw new Error("Failed to generate paste ticket price content");const o=window.open("","_blank","width=400,height=300");return o.document.write(t),o.document.close(),o.focus(),i(`🖨️ ${n("printingPasteTicketPrice",a)||"طباعة ملصق السعر"}`,"info",2e3),!0}catch(o){return console.error("🖨️ Paste Ticket Price Print Error:",o),i(`❌ ${n("printError",a)||"خطأ في الطباعة"}`,"error",3e3),!1}}};export{i as Q,r as R,a as t};
