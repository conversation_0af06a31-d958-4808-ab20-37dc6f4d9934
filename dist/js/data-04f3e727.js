const t=new class{constructor(){this.defaultConfig={servers:{defaultPorts:[3e3,3001,3002,3003,5e3,5001,8080,8081],syncInterval:3e4,requestTimeout:5e3,retryAttempts:3},profit:{useDefaultPrices:!1,defaultMargin:.3,requireValidPrices:!0,excludeInvalidProducts:!0},validation:{strictMode:!0,logWarnings:!0,logErrors:!0},ui:{showDetailedLogs:!1,autoRefreshReports:!0,refreshInterval:6e4},build:{environment:"production",version:"1.0.0",buildDate:(new Date).toISOString()}},this.config=this.loadConfig()}loadConfig(){try{const t=localStorage.getItem("icaldz-config");if(t){const e=JSON.parse(t);return this.mergeConfig(this.defaultConfig,e)}}catch(t){console.warn("خطأ في تحميل التكوين، استخدام الإعدادات الافتراضية:",t)}return{...this.defaultConfig}}saveConfig(){try{localStorage.setItem("icaldz-config",JSON.stringify(this.config)),localStorage.setItem("icaldz-config-timestamp",Date.now().toString())}catch(t){console.error("خطأ في حفظ التكوين:",t)}}mergeConfig(t,e){const r={...t};for(const s in e)e.hasOwnProperty(s)&&("object"!=typeof e[s]||Array.isArray(e[s])?r[s]=e[s]:r[s]=this.mergeConfig(t[s]||{},e[s]));return r}get(t){const e=t.split(".");let r=this.config;for(const s of e){if(!r||!r.hasOwnProperty(s))return;r=r[s]}return r}set(t,e){const r=t.split(".");let s=this.config;for(let n=0;n<r.length-1;n++){const t=r[n];s[t]&&"object"==typeof s[t]||(s[t]={}),s=s[t]}s[r[r.length-1]]=e,this.saveConfig()}reset(){this.config={...this.defaultConfig},this.saveConfig()}getServerList(){const t=new URL(window.location.origin),e=parseInt(t.port)||("https:"===t.protocol?443:80),r=`${t.protocol}//${t.hostname}`,s=this.get("servers.defaultPorts")||[3e3,3001,3002],n=[];return n.push(window.location.origin),s.forEach(t=>{t!==e&&n.push(`${r}:${t}`)}),[...new Set(n)]}validateProfitSettings(){const t=this.get("profit.useDefaultPrices"),e=this.get("profit.requireValidPrices");return t&&e&&console.warn("تحذير: تم تفعيل استخدام الأسعار الافتراضية والتحقق الصارم معاً"),{useDefaultPrices:t,requireValidPrices:e,excludeInvalidProducts:this.get("profit.excludeInvalidProducts")}}getBuildInfo(){return{environment:this.get("build.environment"),version:this.get("build.version"),buildDate:this.get("build.buildDate"),isProduction:"production"===this.get("build.environment"),isDevelopment:"development"===this.get("build.environment")}}};const e=new class{constructor(){this.currentServer=window.location.origin,this.servers=t.getServerList(),this.syncInterval=t.get("servers.syncInterval")||3e4,this.requestTimeout=t.get("servers.requestTimeout")||5e3,this.retryAttempts=t.get("servers.retryAttempts")||3,this.isOnline=navigator.onLine,this.syncTimer=null,this.lastSyncTime=null,window.addEventListener("online",()=>{this.isOnline=!0,this.startSync()}),window.addEventListener("offline",()=>{this.isOnline=!1,this.stopSync()}),this.detectPortChanges()}generateServerList(){const t=new URL(window.location.origin),e=parseInt(t.port)||("https:"===t.protocol?443:80),r=`${t.protocol}//${t.hostname}`,s=[];return s.push(this.currentServer),[3e3,3001,3002,3003,5e3,5001,8080,8081].forEach(t=>{t!==e&&s.push(`${r}:${t}`)}),[...new Set(s)]}detectPortChanges(){const t=window.location.origin;setInterval(()=>{window.location.origin!==t&&(this.currentServer=window.location.origin,this.servers=this.generateServerList(),console.log("Port change detected, updated server list:",this.servers))},5e3)}startSync(){this.syncTimer&&clearInterval(this.syncTimer),this.syncTimer=setInterval(()=>{this.syncAllData()},this.syncInterval),this.syncAllData()}stopSync(){this.syncTimer&&(clearInterval(this.syncTimer),this.syncTimer=null)}async syncAllData(){if(this.isOnline)try{this.lastSyncTime=(new Date).toISOString(),localStorage.setItem("last-sync-time",this.lastSyncTime);const e=["icaldz-sellers","icaldz-products","icaldz-customers","icaldz-invoices","icaldz-purchases","icaldz-suppliers","icaldz-settings","icaldz-expenses"];let r=0;for(const s of e)try{await this.syncDataKey(s),r++}catch(t){console.warn(`فشل في مزامنة ${s}:`,t)}console.log(`Synced ${r} of ${e.length} data keys`)}catch(t){console.error("Data synchronization error:",t)}}async syncDataKey(t){try{const e=localStorage.getItem(t),r=localStorage.getItem(`${t}-timestamp`)||"0",s=await this.getDataFromServers(t);let n={data:e,timestamp:parseInt(r),server:this.currentServer};for(const t of s)t.timestamp>n.timestamp&&(n=t);n.server!==this.currentServer&&(localStorage.setItem(t,n.data),localStorage.setItem(`${t}-timestamp`,n.timestamp.toString()),window.dispatchEvent(new StorageEvent("storage",{key:t,newValue:n.data,oldValue:e}))),n.server===this.currentServer&&await this.broadcastToServers(t,e,parseInt(r))}catch(e){console.error(`خطأ في مزامنة ${t}:`,e)}}async getDataFromServers(t){const e=this.servers.filter(t=>t!==this.currentServer).map(async e=>{try{const r=new AbortController,s=setTimeout(()=>r.abort(),this.requestTimeout),n=await fetch(`${e}/api/sync/${t}`,{method:"GET",signal:r.signal,headers:{"Content-Type":"application/json","Cache-Control":"no-cache"}});if(clearTimeout(s),n.ok){const t=await n.json();return{data:t.data,timestamp:t.timestamp,server:e,success:!0}}console.warn(`خادم ${e} أرجع حالة: ${n.status}`)}catch(r){"AbortError"===r.name?console.warn(`انتهت مهلة الاتصال بالخادم ${e}`):console.warn(`فشل في الاتصال بالخادم ${e}:`,r.message)}return null});return(await Promise.allSettled(e)).filter(t=>"fulfilled"===t.status&&null!==t.value).map(t=>t.value)}async broadcastToServers(t,e,r){if(!this.isOnline)return;const s=this.servers.filter(t=>t!==this.currentServer).map(async s=>{try{const n=new AbortController,i=setTimeout(()=>n.abort(),this.requestTimeout),o=await fetch(`${s}/api/sync/${t}`,{method:"POST",signal:n.signal,headers:{"Content-Type":"application/json","Cache-Control":"no-cache"},body:JSON.stringify({data:e,timestamp:r,source:this.currentServer})});if(clearTimeout(i),o.ok){(await o.json()).success&&console.log(`تم إرسال البيانات بنجاح إلى ${s}`)}}catch(n){"AbortError"===n.name?console.warn(`انتهت مهلة إرسال البيانات إلى ${s}`):console.warn(`فشل في إرسال البيانات إلى ${s}:`,n.message)}});await Promise.allSettled(s)}saveData(t,e){const r=Date.now();localStorage.setItem(t,JSON.stringify(e)),localStorage.setItem(`${t}-timestamp`,r.toString()),this.broadcastToServers(t,JSON.stringify(e),r)}loadData(t,e=null){try{const r=localStorage.getItem(t);return r?JSON.parse(r):e}catch(r){return console.error(`خطأ في تحميل البيانات ${t}:`,r),e}}async forceSyncKey(t){await this.syncDataKey(t)}resetAllData(){Object.keys(localStorage).filter(t=>t.startsWith("icaldz-")).forEach(t=>localStorage.removeItem(t)),window.location.reload()}getSyncStatus(){return{isOnline:this.isOnline,isRunning:null!==this.syncTimer,currentServer:this.currentServer,servers:this.servers,lastSync:localStorage.getItem("last-sync-time")||"لم يتم"}}};"undefined"!=typeof window&&e.startSync();export{e as d};
